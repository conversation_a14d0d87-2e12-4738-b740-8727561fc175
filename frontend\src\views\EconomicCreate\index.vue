<template>
  <div class="body_wrap">
    <div class="part_wrap">
      <div>
        <a-breadcrumb>
          <a-breadcrumb-item><a href="void:0" @click="router.back()"> < 经济分析</a></a-breadcrumb-item>
          <a-breadcrumb-item>创建</a-breadcrumb-item>
        </a-breadcrumb>
      </div>
      
      <!-- 步骤导航 -->
      <div class="steps_wrap">
        <div class="steps_header">
          <div 
            v-for="(step, index) in steps"
            :key="index"
            :class="['step_item', { active: currentStep === index + 1, completed: currentStep > index + 1 }]"
          >
            <div class="step_circle">{{ index + 1 }}</div>
            <div class="step_title">{{ step.title }}</div>
          </div>
        </div>
      </div>

      <div class="content_wrap">
        <a-spin :spinning="state.loading">
          <a-form
            ref="formRef"
            labelAlign="left"
            :model="formState"
            name="economic"
            :label-col="{ span: 9 }"
            :wrapper-col="{ span: 12 }"
            autocomplete="off"
          >
            <!-- 步骤1: 基本信息 -->
            <div v-show="currentStep === 1" class="step_content">
              <div class="box_wrap">
                <div class="b_title">基本信息</div>
                <div class="b_body">
                  <div class="line_item">
                    <a-form-item
                      v-for="item in formBasicInfo()"
                      :key="item.name"
                      class="line_form_item"
                      :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                      :name="item.name"
                      :rules="item.rules"
                      v-show="item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity)"
                    >
                      <a-input
                        v-if="item.type === 'string'"
                        v-model:value="formState[item.name]"
                        :defaultValue="item.default"
                        size="small"
                        style="width: 80%"
                        class="input_deal_wrap"
                      />
                      <a-auto-complete
                        v-else-if="item.type === 'project-select'"
                        v-model:value="formState[item.name]"
                        :options="projectOptions"
                        placeholder="请输入项目名称或从下拉中选择"
                        size="small"
                        style="width: 80%"
                        class="input_deal_wrap"
                        @select="onProjectSelect"
                        @search="onProjectSearch"
                      />
                      <a-textarea
                        v-else-if="item.type === 'textarea'"
                        v-model:value="formState[item.name]"
                        :defaultValue="item.default"
                        size="small"
                        style="width: 80%"
                        :autosize="{ minRows: 2, maxRows: 3 }"
                        class="input_deal_wrap"
                      />
                      <a-input-number
                        v-else-if="item.type === 'number'"
                        v-model:value="formState[item.name]"
                        :defaultValue="item.default"
                        size="small"
                        style="width: 80%"
                        class="input_deal_wrap"
                        :min="0"
                      />
                      <a-select
                        v-else-if="item.type === 'select'"
                        v-model:value="formState[item.name]"
                        :defaultValue="item.default"
                        size="small"
                        @change="onProjectSceneChange"
                      >
                        <a-select-option
                          v-for="option in item.options"
                          :key="option.value"
                          :value="option.value"
                        >
                          {{ option.label }}
                        </a-select-option>
                      </a-select>
                      <a-checkbox-group
                        v-else-if="item.type === 'checkbox-group'"
                        v-model:value="formState[item.name]"
                        @change="onProjectSceneChange"
                        style="display: flex; flex-wrap: wrap; gap: 8px;"
                      >
                        <a-checkbox
                          v-for="option in item.options"
                          :key="option.value"
                          :value="option.value"
                          style="margin-right: 0;"
                        >
                          {{ option.label }}
                        </a-checkbox>
                      </a-checkbox-group>
                    </a-form-item>
                  </div>
                </div>
              </div>
            </div>

            <!-- 步骤2: 资源配置 -->
            <div v-show="currentStep === 2" class="step_content">
              <div class="box_wrap">
                <div class="b_title">资源配置</div>
                <div class="b_body">
                  <div class="scene_content_wrap">
                    <a-tabs v-model:activeKey="activeResourceKey">
                      <!-- 光伏 -->
                      <a-tab-pane v-if="shouldShowModule('pv')" key="pv" tab="光伏">
                        <div class="resource_section">
                          <!-- 投资 -->
                          <div class="resource_sub_section">
                            <div class="sub_title">
                              <span>投资</span>
                              <a-tooltip title="用于辅助计算各投资成本(容量xEPCx占比)，也可直接输入">
                                <a-button 
                                  type="link" 
                                  size="small" 
                                  @click="openAuxCalculateModal('pv', 'investment')"
                                >
                                  <CalculatorOutlined class="auxiliary_icon" />
                                </a-button>
                              </a-tooltip>
                            </div>
                            <div class="line_item">
                              <a-form-item
                                v-for="item in getResourceConfig('pv', 'investment')"
                                :key="item.name"
                                class="line_form_item"
                                :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                                :name="item.name"
                                :rules="item.rules"
                                v-show="item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity)"
                              >
                                <a-input-number
                                  v-if="item.type === 'number' && item.numberType === 'ratio'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
                                  :parser="value => Decimal(value || 0).div(Decimal(100))"
                                  :min="0"
                                  :max="1"
                                  :step="0.01"
                                />
                                <a-input-number
                                  v-else-if="item.type === 'number'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :min="0"
                                />
                              </a-form-item>
                            </div>
                          </div>

                          <!-- 成本 -->
                          <div class="resource_sub_section">
                            <div class="sub_title">
                              <span>成本</span>
                              <a-tooltip title="输入更多参数">
                                <a-button 
                                  v-if="shouldShowExtendButton('pv', 'cost')"
                                  type="link" 
                                  size="small" 
                                  @click="openExtendModal('pv', 'cost')"
                                >
                                  <MoreOutlined class="extend_icon" />
                                </a-button>
                              </a-tooltip>
                            </div>
                            <div class="line_item">
                              <a-form-item
                                v-for="item in getResourceConfig('pv', 'cost')"
                                :key="item.name"
                                class="line_form_item"
                                :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                                :name="item.name"
                                :rules="item.rules"
                                v-show="item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity)"
                              >
                                <a-input-number
                                  v-if="item.type === 'number' && item.numberType === 'ratio'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
                                  :parser="value => Decimal(value || 0).div(Decimal(100))"
                                  :min="0"
                                  :max="1"
                                  :step="0.01"
                                />
                                <a-input-number
                                  v-else-if="item.type === 'number'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :min="0"
                                />
                              </a-form-item>
                            </div>
                          </div>

                          <!-- 损益及其他 -->
                          <div class="resource_sub_section">
                            <div class="sub_title">
                              <span>损益及其他</span>
                              <a-tooltip title="输入更多参数">
                                <a-button 
                                  v-if="shouldShowExtendButton('pv', 'profit')"
                                  type="link" 
                                  size="small" 
                                  @click="openExtendModal('pv', 'profit')"
                                >
                                  <MoreOutlined class="extend_icon" />
                                </a-button>
                              </a-tooltip>
                            </div>
                            <div class="line_item">
                              <a-form-item
                                v-for="item in getResourceConfig('pv', 'profit')"
                                :key="item.name"
                                class="line_form_item"
                                :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                                :name="item.name"
                                :rules="item.rules"
                                v-show="item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity)"
                              >
                                <a-input-number
                                  v-if="item.type === 'number' && item.numberType === 'ratio'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
                                  :parser="value => Decimal(value || 0).div(Decimal(100))"
                                  :min="0"
                                  :max="1"
                                  :step="0.01"
                                />
                                <a-input-number
                                  v-else-if="item.type === 'number'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :min="0"
                                />
                              </a-form-item>
                            </div>
                          </div>
                        </div>
                      </a-tab-pane>

                      <!-- 风机 -->
                      <a-tab-pane v-if="shouldShowModule('wind')" key="wind" tab="风机">
                        <div class="resource_section">
                          <!-- 投资 -->
                          <div class="resource_sub_section">
                            <div class="sub_title">
                              <span>投资</span>
                              <a-tooltip title="用于辅助计算各投资成本(容量xEPCx占比)，也可直接输入">
                                <a-button 
                                  type="link" 
                                  size="small" 
                                  @click="openAuxCalculateModal('wind', 'investment')"
                                >
                                  <CalculatorOutlined class="auxiliary_icon" />
                                </a-button>
                              </a-tooltip>
                            </div>
                            <div class="line_item">
                              <a-form-item
                                v-for="item in getResourceConfig('wind', 'investment')"
                                :key="item.name"
                                class="line_form_item"
                                :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                                :name="item.name"
                                :rules="item.rules"
                                v-show="item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity)"
                              >
                                <a-input-number
                                  v-if="item.type === 'number' && item.numberType === 'ratio'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
                                  :parser="value => Decimal(value || 0).div(Decimal(100))"
                                  :min="0"
                                  :max="1"
                                  :step="0.01"
                                />
                                <a-input-number
                                  v-else-if="item.type === 'number'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :min="0"
                                />
                              </a-form-item>
                            </div>
                          </div>

                          <!-- 成本 -->
                          <div class="resource_sub_section">
                            <div class="sub_title">
                              <span>成本</span>
                              <a-tooltip title="输入更多参数">
                                <a-button 
                                  v-if="shouldShowExtendButton('wind', 'cost')"
                                  type="link" 
                                  size="small" 
                                  @click="openExtendModal('wind', 'cost')"
                                >
                                  <MoreOutlined class="extend_icon" />
                                </a-button>
                              </a-tooltip>
                            </div>
                            <div class="line_item">
                              <a-form-item
                                v-for="item in getResourceConfig('wind', 'cost')"
                                :key="item.name"
                                class="line_form_item"
                                :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                                :name="item.name"
                                :rules="item.rules"
                                v-show="item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity)"
                              >
                                <a-input-number
                                  v-if="item.type === 'number' && item.numberType === 'ratio'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
                                  :parser="value => Decimal(value || 0).div(Decimal(100))"
                                  :min="0"
                                  :max="1"
                                  :step="0.01"
                                />
                                <a-input-number
                                  v-else-if="item.type === 'number'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :min="0"
                                />
                              </a-form-item>
                            </div>
                          </div>

                          <!-- 损益及其他 -->
                          <div class="resource_sub_section" v-if="getResourceConfig('wind', 'profit')?.filter(item => item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity))?.length">
                            <div class="sub_title">
                              <span>损益及其他</span>
                              <a-tooltip title="输入更多参数">
                                <a-button 
                                  v-if="shouldShowExtendButton('wind', 'profit')"
                                  type="link" 
                                  size="small" 
                                  @click="openExtendModal('wind', 'profit')"
                                >
                                  <MoreOutlined class="extend_icon" />
                                </a-button>
                              </a-tooltip>
                            </div>
                            <div class="line_item">
                              <a-form-item
                                v-for="item in getResourceConfig('wind', 'profit')"
                                :key="item.name"
                                class="line_form_item"
                                :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                                :name="item.name"
                                :rules="item.rules"
                                v-show="item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity)"
                              >
                                <a-input-number
                                  v-if="item.type === 'number' && item.numberType === 'ratio'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
                                  :parser="value => Decimal(value || 0).div(Decimal(100))"
                                  :min="0"
                                  :max="1"
                                  :step="0.01"
                                />
                                <a-input-number
                                  v-else-if="item.type === 'number'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :min="0"
                                />
                              </a-form-item>
                            </div>
                          </div>
                        </div>
                      </a-tab-pane>

                      <!-- 电网 -->
                      <a-tab-pane v-if="shouldShowModule('grid')" key="grid" tab="电网">
                        <div class="resource_section">
                          <!-- 配置 -->
                          <div class="resource_sub_section">
                            <div class="sub_title">
                              <span>运营参数</span>
                            </div>
                            <div class="line_item">
                                                             <a-form-item
                                 v-for="item in getResourceConfig('grid', 'config')"
                                 :key="item.name"
                                 class="line_form_item"
                                 :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                                 :name="item.name"
                                 :rules="item.rules"
                                 v-show="item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity)"
                              >
                                <a-input-number
                                  v-if="item.type === 'number' && item.numberType === 'ratio'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
                                  :parser="value => Decimal(value || 0).div(Decimal(100))"
                                  :min="0"
                                  :max="1"
                                  :step="0.01"
                                />
                                <a-input-number
                                  v-else-if="item.type === 'number'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :min="0"
                                />
                              </a-form-item>
                            </div>
                          </div>
                        </div>
                      </a-tab-pane>

                      <!-- 储能 -->
                      <a-tab-pane v-if="shouldShowModule('storage')" key="storage" tab="储能">
                        <div class="resource_section">
                          <!-- 投资 -->
                          <div class="resource_sub_section">
                            <div class="sub_title">
                              <span>投资</span>
                              <a-tooltip title="用于辅助计算各投资成本(容量xEPCx占比)，也可直接输入">
                                <a-button 
                                  type="link" 
                                  size="small" 
                                  @click="openAuxCalculateModal('storage', 'investment')"
                                >
                                  <CalculatorOutlined class="auxiliary_icon" />
                                </a-button>
                              </a-tooltip>
                            </div>
                            <div class="line_item">
                              <a-form-item
                                v-for="item in getResourceConfig('storage', 'investment')"
                                :key="item.name"
                                class="line_form_item"
                                :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                                :name="item.name"
                                :rules="item.rules"
                                v-show="item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity)"
                              >
                                <a-input-number
                                  v-if="item.type === 'number' && item.numberType === 'ratio'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
                                  :parser="value => Decimal(value || 0).div(Decimal(100))"
                                  :min="0"
                                  :max="1"
                                  :step="0.01"
                                />
                                <a-input-number
                                  v-else-if="item.type === 'number'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :min="0"
                                />
                              </a-form-item>
                            </div>
                          </div>

                          <!-- 成本 -->
                          <div class="resource_sub_section">
                            <div class="sub_title">
                              <span>成本</span>
                              <a-tooltip title="输入更多参数">
                                <a-button 
                                  v-if="shouldShowExtendButton('storage', 'cost')"
                                  type="link" 
                                  size="small" 
                                  @click="openExtendModal('storage', 'cost')"
                                >
                                  <MoreOutlined class="extend_icon" />
                                </a-button>
                              </a-tooltip>
                            </div>
                            <div class="line_item">
                              <a-form-item
                                v-for="item in getResourceConfig('storage', 'cost')"
                                :key="item.name"
                                class="line_form_item"
                                :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                                :name="item.name"
                                :rules="item.rules"
                                v-show="item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity)"
                              >
                                <a-input-number
                                  v-if="item.type === 'number' && item.numberType === 'ratio'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
                                  :parser="value => Decimal(value || 0).div(Decimal(100))"
                                  :min="0"
                                  :max="1"
                                  :step="0.01"
                                />
                                <a-input-number
                                  v-else-if="item.type === 'number'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :min="0"
                                />
                              </a-form-item>
                            </div>
                          </div>

                          <!-- 损益及其他 -->
                          <div class="resource_sub_section" v-if="getResourceConfig('storage', 'profit')?.length">
                            <div class="sub_title">
                              <span>损益及其他</span>
                              <a-tooltip title="输入更多参数">
                                <a-button 
                                  v-if="shouldShowExtendButton('storage', 'profit')"
                                  type="link" 
                                  size="small" 
                                  @click="openExtendModal('storage', 'profit')"
                                >
                                  <MoreOutlined class="extend_icon" />
                                </a-button>
                              </a-tooltip>
                            </div>
                            <div class="line_item">
                              <a-form-item
                                v-for="item in getResourceConfig('storage', 'profit')"
                                :key="item.name"
                                class="line_form_item"
                                :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                                :name="item.name"
                                :rules="item.rules"
                                v-show="item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity)"
                              >
                                <a-input-number
                                  v-if="item.type === 'number' && item.numberType === 'ratio'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
                                  :parser="value => Decimal(value || 0).div(Decimal(100))"
                                  :min="0"
                                  :max="1"
                                  :step="0.01"
                                />
                                <a-input-number
                                  v-else-if="item.type === 'number'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :min="0"
                                />
                              </a-form-item>
                            </div>
                          </div>
                        </div>
                      </a-tab-pane>

                      <!-- 制氢 -->
                      <a-tab-pane v-if="shouldShowModule('hydrogen')" key="hydrogen" tab="制氢">
                        <div class="resource_section">
                          <!-- 投资 -->
                          <div class="resource_sub_section">
                            <div class="sub_title">
                              <span>投资</span>
                              <a-tooltip title="用于辅助计算各投资成本(容量xEPCx占比)，也可直接输入">
                                <a-button 
                                  type="link" 
                                  size="small" 
                                  @click="openAuxCalculateModal('hydrogen', 'investment')"
                                >
                                  <CalculatorOutlined class="auxiliary_icon" />
                                </a-button>
                              </a-tooltip>
                            </div>
                            <div class="line_item">
                              <a-form-item
                                v-for="item in getResourceConfig('hydrogen', 'investment')"
                                :key="item.name"
                                class="line_form_item"
                                :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                                :name="item.name"
                                :rules="item.rules"
                                v-show="item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity)"
                              >
                                <a-input-number
                                  v-if="item.type === 'number' && item.numberType === 'ratio'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
                                  :parser="value => Decimal(value || 0).div(Decimal(100))"
                                  :min="0"
                                  :max="1"
                                  :step="0.01"
                                />
                                <a-input-number
                                  v-else-if="item.type === 'number'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :min="0"
                                />
                              </a-form-item>
                            </div>
                          </div>

                          <!-- 成本 -->
                          <div class="resource_sub_section">
                            <div class="sub_title">
                              <span>成本</span>
                              <a-tooltip title="输入更多参数">
                                <a-button 
                                  v-if="shouldShowExtendButton('hydrogen', 'cost')"
                                  type="link" 
                                  size="small" 
                                  @click="openExtendModal('hydrogen', 'cost')"
                                >
                                  <MoreOutlined class="extend_icon" />
                                </a-button>
                              </a-tooltip>
                            </div>
                            <div class="line_item">
                              <a-form-item
                                v-for="item in getResourceConfig('hydrogen', 'cost')"
                                :key="item.name"
                                class="line_form_item"
                                :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                                :name="item.name"
                                :rules="item.rules"
                                v-show="item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity)"
                              >
                                <a-input-number
                                  v-if="item.type === 'number' && item.numberType === 'ratio'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
                                  :parser="value => Decimal(value || 0).div(Decimal(100))"
                                  :min="0"
                                  :max="1"
                                  :step="0.01"
                                />
                                <a-input-number
                                  v-else-if="item.type === 'number'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :min="0"
                                />
                              </a-form-item>
                            </div>
                          </div>

                          <!-- 损益及其他 -->
                          <div class="resource_sub_section">
                            <div class="sub_title">
                              <span>损益及其他</span>
                              <a-tooltip title="输入更多参数">
                                <a-button 
                                  v-if="shouldShowExtendButton('hydrogen', 'profit')"
                                  type="link" 
                                  size="small" 
                                  @click="openExtendModal('hydrogen', 'profit')"
                                >
                                  <MoreOutlined class="extend_icon" />
                                </a-button>
                              </a-tooltip>
                            </div>
                            <div class="line_item">
                              <a-form-item
                                v-for="item in getResourceConfig('hydrogen', 'profit')"
                                :key="item.name"
                                class="line_form_item"
                                :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                                :name="item.name"
                                :rules="item.rules"
                                v-show="item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity)"
                              >
                                <a-input-number
                                  v-if="item.type === 'number' && item.numberType === 'ratio'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
                                  :parser="value => Decimal(value || 0).div(Decimal(100))"
                                  :min="0"
                                  :max="1"
                                  :step="0.01"
                                />
                                <a-input-number
                                  v-else-if="item.type === 'number'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :min="0"
                                />
                              </a-form-item>
                            </div>
                          </div>
                        </div>
                      </a-tab-pane>

                      <!-- 储氢 -->
                      <a-tab-pane v-if="shouldShowModule('hydrogenStorage')" key="hydrogenStorage" tab="储氢">
                        <div class="resource_section">
                          <!-- 投资 -->
                          <div class="resource_sub_section">
                            <div class="sub_title">
                              <span>投资</span>
                              <a-tooltip title="用于辅助计算各投资成本(容量xEPCx占比)，也可直接输入">
                                <a-button 
                                  type="link" 
                                  size="small" 
                                  @click="openAuxCalculateModal('hydrogenStorage', 'investment')"
                                >
                                  <CalculatorOutlined class="auxiliary_icon" />
                                </a-button>
                              </a-tooltip>
                            </div>
                            <div class="line_item">
                              <a-form-item
                                v-for="item in getResourceConfig('hydrogenStorage', 'investment')"
                                :key="item.name"
                                class="line_form_item"
                                :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                                :name="item.name"
                                :rules="item.rules"
                                v-show="item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity)"
                              >
                                <a-input-number
                                  v-if="item.type === 'number' && item.numberType === 'ratio'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
                                  :parser="value => Decimal(value || 0).div(Decimal(100))"
                                  :min="0"
                                  :max="1"
                                  :step="0.01"
                                />
                                <a-input-number
                                  v-else-if="item.type === 'number'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :min="0"
                                />
                              </a-form-item>
                            </div>
                          </div>

                          <!-- 成本 -->
                          <div class="resource_sub_section">
                            <div class="sub_title">
                              <span>成本</span>
                              <a-tooltip title="输入更多参数">
                                <a-button 
                                  v-if="shouldShowExtendButton('hydrogenStorage', 'cost')"
                                  type="link" 
                                  size="small" 
                                  @click="openExtendModal('hydrogenStorage', 'cost')"
                                >
                                  <MoreOutlined class="extend_icon" />
                                </a-button>
                              </a-tooltip>
                            </div>
                            <div class="line_item">
                              <a-form-item
                                v-for="item in getResourceConfig('hydrogenStorage', 'cost')"
                                :key="item.name"
                                class="line_form_item"
                                :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                                :name="item.name"
                                :rules="item.rules"
                                v-show="item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity)"
                              >
                                <a-input-number
                                  v-if="item.type === 'number' && item.numberType === 'ratio'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
                                  :parser="value => Decimal(value || 0).div(Decimal(100))"
                                  :min="0"
                                  :max="1"
                                  :step="0.01"
                                />
                                <a-input-number
                                  v-else-if="item.type === 'number'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :min="0"
                                />
                              </a-form-item>
                            </div>
                          </div>

                          <!-- 损益及其他 -->
                          <div class="resource_sub_section" v-if="getResourceConfig('hydrogenStorage', 'profit')?.length">
                            <div class="sub_title">
                              <span>损益及其他</span>
                              <a-tooltip title="输入更多参数">
                                <a-button 
                                  v-if="shouldShowExtendButton('hydrogenStorage', 'profit')"
                                  type="link" 
                                  size="small" 
                                  @click="openExtendModal('hydrogenStorage', 'profit')"
                                >
                                  <MoreOutlined class="extend_icon" />
                                </a-button>
                              </a-tooltip>
                            </div>
                            <div class="line_item">
                              <a-form-item
                                v-for="item in getResourceConfig('hydrogenStorage', 'profit')"
                                :key="item.name"
                                class="line_form_item"
                                :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                                :name="item.name"
                                :rules="item.rules"
                                v-show="item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity)"
                              >
                                <a-input-number
                                  v-if="item.type === 'number' && item.numberType === 'ratio'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
                                  :parser="value => Decimal(value || 0).div(Decimal(100))"
                                  :min="0"
                                  :max="1"
                                  :step="0.01"
                                />
                                <a-input-number
                                  v-else-if="item.type === 'number'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :min="0"
                                />
                              </a-form-item>
                            </div>
                          </div>
                        </div>
                      </a-tab-pane>

                      <!-- 其他 (始终显示) -->
                      <a-tab-pane key="other" tab="其他">
                        <div class="resource_section">
                          <!-- 运营参数 -->
                          <div class="resource_sub_section">
                            <div class="sub_title">
                              <span>运营参数</span>
                            </div>
                            <div class="line_item">
                              <a-form-item
                                v-for="item in formOperationConfig()"
                                :key="item.name"
                                class="line_form_item"
                                :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                                :name="item.name"
                                :rules="item.rules"
                                v-show="item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity)"
                              >
                                <a-input-number
                                  v-if="item.type === 'number' && item.numberType === 'ratio'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
                                  :parser="value => Decimal(value || 0).div(Decimal(100))"
                                  :min="0"
                                  :max="1"
                                  :step="0.01"
                                />
                                <a-input-number
                                  v-else-if="item.type === 'number'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :min="0"
                                />
                              </a-form-item>
                            </div>
                          </div>

                          <!-- 人工成本 -->
                          <div class="resource_sub_section">
                            <div class="sub_title">
                              <span>人工成本</span>
                            </div>
                            <div class="line_item">
                              <a-form-item
                                v-for="item in formLaborCostConfig()"
                                :key="item.name"
                                class="line_form_item"
                                :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                                :name="item.name"
                                :rules="item.rules"
                                v-show="item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity)"
                              >
                                <a-input-number
                                  v-if="item.type === 'number' && item.numberType === 'ratio'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
                                  :parser="value => Decimal(value || 0).div(Decimal(100))"
                                  :min="0"
                                  :max="1"
                                  :step="0.01"
                                />
                                <a-input-number
                                  v-else-if="item.type === 'number'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :min="0"
                                />
                              </a-form-item>
                            </div>
                          </div>

                          <!-- 土地租赁 -->
                          <div class="resource_sub_section">
                            <div class="sub_title">
                              <span>土地租赁</span>
                            </div>
                            <div class="line_item">
                              <a-form-item
                                v-for="item in formLandLeaseConfig()"
                                :key="item.name"
                                class="line_form_item"
                                :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                                :name="item.name"
                                :rules="item.rules"
                                v-show="item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity)"
                              >
                                <a-input-number
                                  v-if="item.type === 'number' && item.numberType === 'ratio'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
                                  :parser="value => Decimal(value || 0).div(Decimal(100))"
                                  :min="0"
                                  :max="1"
                                  :step="0.01"
                                />
                                <a-input-number
                                  v-else-if="item.type === 'number'"
                                  v-model:value="formState[item.name]"
                                  :defaultValue="item.default"
                                  size="small"
                                  class="input_deal_wrap"
                                  :min="0"
                                />
                              </a-form-item>
                            </div>
                          </div>
                        </div>
                      </a-tab-pane>
                    </a-tabs>
                  </div>
                </div>
              </div>
            </div>

            <!-- 步骤3: 投资数据 -->
            <div v-show="currentStep === 3" class="step_content">
              <div class="box_wrap">
                <div class="b_title">融资</div>
                <div class="b_body">
                  <div class="line_item">
                    <a-form-item
                      v-for="item in formFinancingConfig()"
                      :key="item.name"
                      class="line_form_item"
                      :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                      :name="item.name"
                      :rules="item.rules"
                    >
                      <a-input-number
                        v-if="item.type === 'number' && item.numberType === 'ratio'"
                        v-model:value="formState[item.name]"
                        :defaultValue="item.default"
                        size="small"
                        class="input_deal_wrap"
                        :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
                        :parser="value => Decimal(value || 0).div(Decimal(100))"
                        :min="0"
                        :max="1"
                        :step="0.01"
                      />
                      <a-input-number
                        v-else-if="item.type === 'number'"
                        v-model:value="formState[item.name]"
                        :defaultValue="item.default"
                        size="small"
                        class="input_deal_wrap"
                        :min="0"
                      />
                      <a-select
                        v-else-if="item.type === 'select'"
                        v-model:value="formState[item.name]"
                        :defaultValue="item.default"
                        size="small"
                      >
                        <a-select-option
                          v-for="option in item.options"
                          :key="option.value"
                          :value="option.value"
                        >
                          {{ option.label }}
                        </a-select-option>
                      </a-select>
                    </a-form-item>
                  </div>
                </div>
              </div>

              <div class="box_wrap">
                <div class="b_title">税率</div>
                <div class="b_body">
                  <div class="line_item">
                    <a-form-item
                      v-for="item in formTaxConfig()"
                      :key="item.name"
                      class="line_form_item"
                      :label="item.unit ? `${item.label}(${item.unit})` : item.label"
                      :name="item.name"
                      :rules="item.rules"
                    >
                      <a-input-number
                        v-if="item.type === 'number' && item.numberType === 'ratio'"
                        v-model:value="formState[item.name]"
                        :defaultValue="item.default"
                        size="small"
                        class="input_deal_wrap"
                        :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
                        :parser="value => Decimal(value || 0).div(Decimal(100))"
                        :min="0"
                        :max="1"
                        :step="0.01"
                      />
                      <a-input-number
                        v-else-if="item.type === 'number'"
                        v-model:value="formState[item.name]"
                        :defaultValue="item.default"
                        size="small"
                        class="input_deal_wrap"
                        :min="0"
                      />
                    </a-form-item>
                  </div>
                </div>
              </div>
            </div>

            <!-- 步骤4: 经济分析 -->
            <div v-show="currentStep === 4" class="step_content">
              <div class="analysis_loading">
                <div class="loading_content">
                  <div class="loading_icon">
                    <div class="loading_circle"></div>
                  </div>
                  <div class="loading_text">经济性分析</div>
                  <div class="loading_desc">请稍候，系统正在计算项目的经济指标...</div>
                </div>
              </div>
            </div>
          </a-form>
        </a-spin>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="button_wrap" :style="{ 
      // right: '20px',
      // marginleft: baseStore.menuCollapsed ? '100px' : '180px',
      // width: baseStore.menuCollapsed ? 'calc(100% - 100px)' : 'calc(100% - 180px)'
    }">
      <div class="button_group">
        <a-button 
          v-if="currentStep > 1 && currentStep < 4"
          @click="prevStep" 
        >
          上一步
        </a-button>
        <a-button 
          v-if="currentStep < 3"
          @click="nextStep" 
          type="primary" 
        >
          下一步
        </a-button>
        <a-button 
          v-if="currentStep === 3"
          @click="startAnalysis" 
          type="primary" 
        >
          开始分析
        </a-button>
      </div>
    </div>

    <!-- 辅助计算弹窗 -->
    <a-modal
      v-model:open="auxCalculateModal.visible"
      :title="auxCalculateModal.title"
      @ok="submitAuxCalculate"
      @cancel="auxCalculateModal.visible = false"
      width="600px"
    >
      <a-form
        ref="auxFormRef"
        :model="auxCalculateModal.formData"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item
          v-for="item in auxCalculateModal.config"
          :key="item.name"
          :label="item.unit ? `${item.label}(${item.unit})` : item.label"
          :name="item.name"
          :rules="item.rules"
          v-show="item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity)"
        >
          <a-input-number
            v-if="item.type === 'number' && item.numberType === 'ratio'"
            v-model:value="auxCalculateModal.formData[item.name]"
            :defaultValue="item.default"
            size="small"
            :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
            :parser="value => Decimal(value || 0).div(Decimal(100))"
            :min="0"
            :max="1"
            :step="0.01"
          />
          <a-input-number
            v-else-if="item.type === 'number'"
            v-model:value="auxCalculateModal.formData[item.name]"
            :defaultValue="item.default"
            size="small"
            :min="0"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 扩展参数弹窗 -->
    <a-modal
      v-model:open="extendModal.visible"
      :title="extendModal.title"
      @ok="submitExtendParams"
      @cancel="extendModal.visible = false"
      width="800px"
    >
      <a-form
        ref="extendFormRef"
        :model="extendModal.formData"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item
          v-for="item in extendModal.config"
          :key="item.name"
          :label="item.unit ? `${item.label}(${item.unit})` : item.label"
          :name="item.name"
          :rules="item.rules"
          v-show="item.visible !== false && !(item.notShowFromCapacity === true && isFromCapacity)"
        >
          <a-input-number
            v-if="item.type === 'number' && item.numberType === 'ratio'"
            v-model:value="extendModal.formData[item.name]"
            :defaultValue="item.default"
            size="small"
            :formatter="value => `${Decimal(value || 0).mul(Decimal(100))}`"
            :parser="value => Decimal(value || 0).div(Decimal(100))"
            :min="0"
            :max="1"
            :step="0.01"
          />
          <a-input-number
            v-else-if="item.type === 'number'"
            v-model:value="extendModal.formData[item.name]"
            :defaultValue="item.default"
            size="small"
            :min="0"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed, watch } from 'vue'
import Decimal from "decimal.js"
import { message } from 'ant-design-vue'
import { useRouter, useRoute } from 'vue-router'
import { useBaseStore } from '@/stores/base'
import { submitEconomicTask, getEconomicProject, getEconomicSolutionInfo } from '@/api/economic'
import { getSolution, getResult } from '@/api/project'
import { CalculatorOutlined, MoreOutlined } from '@ant-design/icons-vue'
import { saveNunberPoint } from '@/util'
import {
  formBasicInfo,
  formOtherConfig,
  formOperationConfig,
  formLaborCostConfig,
  formLandLeaseConfig,
  formFinancingConfig,
  formTaxConfig,
  getResourceConfig,
  getAuxCalculateConfig,
  getExtendConfig
} from './utils'
import { economicFormDefaultVal } from './initValue'

const router = useRouter()
const route = useRoute()
const baseStore = useBaseStore()
const formRef = ref()
const auxFormRef = ref()
const extendFormRef = ref()
const solutionParams = ref({})
const isFromCapacity = computed(
  () => (route.query.capSolutionId && route.query.capProjectId) || solutionParams.value.isFromCapacity
)
const capParams = ref({})
const state = reactive({
  loading: false,
  submitting: false
})

const formState = ref({})
const currentStep = ref(1)
const activeResourceKey = ref('pv')
const ecoProjectList = ref([])
const selectedProjectId = ref(undefined) // 选中的项目ID
const projectOptions = ref([]) // 项目选项（用于自动完成）

const steps = [
  { title: '基本信息' },
  { title: '资源配置' },
  { title: '投资数据' },
  { title: '经济分析' }
]

// 辅助计算弹窗
const auxCalculateModal = reactive({
  visible: false,
  title: '',
  config: [],
  formData: {},
  currentModule: '',
  currentSection: ''
})

// 扩展参数弹窗
const extendModal = reactive({
  visible: false,
  title: '',
  config: [],
  formData: {},
  currentModule: '',
  currentSection: ''
})

// 为每个模块维护独立的辅助计算数据
const auxCalculateData = reactive({
  pv: { investment: {} },
  wind: { investment: {} },
  storage: { investment: {} },
  hydrogen: { investment: {} },
  hydrogenStorage: { investment: {} }
})

const getEcoProjectList = async () => {
  const { code, data: { total: t, result }, msg } = await getEconomicProject({
    pageSize: -1,
    pageNumber: 1
  })
  if (code === 0) {
    ecoProjectList.value = result || []
    // 构建自动完成选项
    projectOptions.value = (result || []).map(project => ({
      value: project.name,
      label: project.name,
      projectData: project // 保存完整的项目数据
    }))
    console.log('项目列表:', result)
  } else {
    message.error(msg)
  }
}

// 根据项目场景判断是否显示模块
const shouldShowModule = (moduleType) => {
  const scene = formState.value.projectScene
  if (!scene || !Array.isArray(scene)) return false
  
  // 新的checkbox格式处理：数组中选中的模块值对应索引位置
  const moduleMapping = {
    'pv': 0,      // 光伏
    'wind': 1,    // 风电  
    'grid': 2,    // 电网
    'storage': 3, // 储能
    'hydrogen': 4,    // 制氢
    'hydrogenStorage': 5
  }
  
  // 如果是选中的value数组格式 ['pv', 'wind', 'hydrogen']
  if (scene.length > 0 && typeof scene[0] === 'string') {
    return scene.includes(moduleType)
  }
  
  // 如果是0,1数组格式 [1,1,0,1,1,0]
  const moduleIndex = moduleMapping[moduleType]
  return moduleIndex !== undefined && scene[moduleIndex] === 1
}

// 获取当前可显示的资源配置键
const getActiveResourceKeys = () => {
  const keys = []
  const moduleMap = {
    'pv': 'pv',
    'wind': 'wind',
    'grid': 'grid',
    'storage': 'storage',
    'hydrogen': 'hydrogen',
    'hydrogenStorage': 'hydrogenStorage'
  }
  
  Object.entries(moduleMap).forEach(([key, value]) => {
    if (shouldShowModule(key)) {
      keys.push(value)
    }
  })
  
  return keys
}

// 项目选择变化（从下拉选项中选择）
const onProjectSelect = (value, option) => {
  console.log('项目选择变化:', value, option)
  
  if (option && option.projectData) {
    // 选中了项目，使用项目数据填充表单
    const selectedProject = option.projectData
    selectedProjectId.value = selectedProject.id
    formState.value.projectName = selectedProject.name
    formState.value.customerName = selectedProject.customer
    formState.value.desc = selectedProject.desc
    console.log('选中项目，填充表单数据:', selectedProject)
  }
}

// 搜索/输入变化
const onProjectSearch = (value) => {
  console.log('搜索/输入变化:', value)
  
  // 如果当前输入值与任何项目名称不匹配，清空选中的项目ID
  const matchedProject = ecoProjectList.value.find(p => p.name === value)
  if (!matchedProject && selectedProjectId.value) {
    selectedProjectId.value = undefined
    console.log('手动输入项目名称，清空选中项目')
  }
}

// 项目场景变化
const onProjectSceneChange = (value) => {
  console.log('项目场景变化:', value)
  
  // 如果是checkbox-group类型，需要转换为0,1数组格式输出
  if (Array.isArray(value) && value.length > 0 && typeof value[0] === 'string') {
    const moduleMapping = {
      'pv': 0,
      'wind': 1,
      'grid': 2,
      'storage': 3,
      'hydrogen': 4,
      'hydrogenStorage': 5
    }
    
    // 创建0,1数组
    const binaryArray = [0, 0, 0, 0, 0, 0]
    value.forEach(moduleValue => {
      const index = moduleMapping[moduleValue]
      if (index !== undefined) {
        binaryArray[index] = 1
      }
    })
    
    console.log('转换后的数组格式:', binaryArray)
    // 这里可以将binaryArray存储到需要的地方，或者添加到表单数据中
    formState.value.projectSceneBinary = binaryArray
  }
}

// 步骤控制
const nextStep = async () => {
  try {
    await formRef.value.validateFields()
    if (currentStep.value < 3) {
      currentStep.value++
    }
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}

const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

// 打开辅助计算弹窗
const openAuxCalculateModal = (module, section) => {
  const config = getAuxCalculateConfig(module, section)
  if (!config || config.length === 0) return
  
  auxCalculateModal.visible = true
  auxCalculateModal.title = `${getModuleDisplayName(module)}${getSectionDisplayName(section)}辅助计算`
  auxCalculateModal.config = config
  auxCalculateModal.currentModule = module
  auxCalculateModal.currentSection = section
  
  // 获取该模块的已保存数据或设置默认值
  const savedData = auxCalculateData[module]?.[section] || {}
  auxCalculateModal.formData = {}
  
  config.forEach(item => {
    // 优先级：已保存的辅助计算数据 > 当前表单值 > 配置默认值 > 全局默认值
    if (savedData[item.name] !== undefined) {
      auxCalculateModal.formData[item.name] = savedData[item.name]
    } else if (formState.value[item.name] !== undefined) {
      auxCalculateModal.formData[item.name] = formState.value[item.name]
    } else if (item.default !== undefined) {
      auxCalculateModal.formData[item.name] = item.default
    } else if (economicFormDefaultVal[item.name] !== undefined) {
      auxCalculateModal.formData[item.name] = economicFormDefaultVal[item.name]
    }
  })
}

// 获取模块显示名称
const getModuleDisplayName = (module) => {
  const names = {
    'pv': '光伏',
    'wind': '风机',
    'storage': '储能',
    'hydrogen': '制氢',
    'hydrogenStorage': '储氢'
  }
  return names[module] || module
}

// 获取部分显示名称
const getSectionDisplayName = (section) => {
  const names = {
    'investment': '投资',
    'cost': '成本',
    'profit': '损益及其他'
  }
  return names[section] || section
}

// 判断是否应该显示扩展参数按钮
const shouldShowExtendButton = (module, section) => {
  const config = getExtendConfig(module, section)
  return config && config.length > 0
}

// 提交辅助计算
const submitAuxCalculate = async () => {
  try {
    await auxFormRef.value.validateFields()
    
    // 保存当前模块的辅助计算数据
    const { currentModule, currentSection, formData } = auxCalculateModal
    if (!auxCalculateData[currentModule]) {
      auxCalculateData[currentModule] = {}
    }
    auxCalculateData[currentModule][currentSection] = { ...formData }
    
    // 将辅助计算弹窗中的数据同步到主表单
    Object.keys(formData).forEach(key => {
      formState.value[key] = formData[key]
      // console.log(`同步${key}:`, formData[key])
    })
    
    // 使用通用计算函数
    calculateModuleInvestment(currentModule, currentSection, formData)
    
    auxCalculateModal.visible = false
    message.success('计算完成')
  } catch (error) {
    console.log('辅助计算验证失败:', error)
  }
}

// 打开扩展参数弹窗
const openExtendModal = (module, section) => {
  const config = getExtendConfig(module, section)
  if (!config || config.length === 0) {
    console.log(`${module}-${section} 扩展参数配置为空，不显示弹窗`)
    return
  }
  
  extendModal.visible = true
  extendModal.title = `${getModuleDisplayName(module)}${getSectionDisplayName(section)}扩展参数`
  extendModal.config = config
  extendModal.currentModule = module
  extendModal.currentSection = section
  extendModal.formData = {}
  
  // 从主表单中获取当前值，如果没有则从默认值中获取
  config.forEach(item => {
    extendModal.formData[item.name] = formState.value[item.name] || item.default || economicFormDefaultVal[item.name]
  })
}

// 提交扩展参数
const submitExtendParams = async () => {
  try {
    await extendFormRef.value.validateFields()
    
    // 将扩展参数值同步到主表单
    Object.keys(extendModal.formData).forEach(key => {
      formState.value[key] = extendModal.formData[key]
    })
    
    extendModal.visible = false
    message.success('参数已保存')
  } catch (error) {
    console.log('扩展参数验证失败:', error)
  }
}

// 提交分析
// 开始分析 - 跳转到第4步
const startAnalysis = async () => {
  try {
    await formRef.value.validateFields()
    
    currentStep.value = 4

    await submitAnalysis()
  } catch (error) {
    console.log('表单验证失败:', error)
    message.error('请检查表单数据')
  }
}

// 提交经济分析
const submitAnalysis = async () => {
  try {
    state.submitting = true
    
    // 准备提交数据，确保项目场景为0,1数组格式
    const submitData = { ...formState.value }
    
    // 转换项目场景格式
    if (Array.isArray(submitData.projectScene) && submitData.projectScene.length > 0 && typeof submitData.projectScene[0] === 'string') {
      const moduleMapping = {
        'pv': 0,
        'wind': 1,
        'grid': 2,
        'storage': 3,
        'hydrogen': 4,
        'hydrogenStorage': 5
      }
      
      const binaryArray = [0, 0, 0, 0, 0, 0]
      submitData.projectScene.forEach(moduleValue => {
        const index = moduleMapping[moduleValue]
        if (index !== undefined) {
          binaryArray[index] = 1
        }
      })
      
      submitData.projectScene = binaryArray
    }
    
    console.log('提交数据:', submitData)
    const params = {}
    
    // 构建 project 对象，如果选择了项目则添加 id 字段
    const projectData = {
      name: submitData.projectName,
      customer: submitData.customerName,
      desc: submitData.desc,
    }
    
    // 如果选择了项目，添加项目ID
    if (selectedProjectId.value) {
      projectData.id = selectedProjectId.value
    }
    console.log('submitData', submitData)
    const { projectScene: [pvShow, windShow, gridShow, batShow, alkShow, alkStoreShow] } = submitData


    const allParams = {
      project: projectData,
      solution: {
        topology: submitData.projectScene,
        desc: submitData.solutionDesc
      },
      calcParams: {
        ...submitData,
      }
    }

    if (!pvShow) {
      allParams.calcParams.pvCapacity = 0
      allParams.calcParams.photovoltaicEquipment = 0
      allParams.calcParams.photovoltaicInstallation = 0
      allParams.calcParams.photovoltaicBuilding = 0
      allParams.calcParams.photovoltaicOthers = 0
    }
    if (!windShow) {
      allParams.calcParams.windCapacity = 0
      allParams.calcParams.windTurbineEquipment = 0
      allParams.calcParams.windTurbineInstallation = 0
      allParams.calcParams.windTurbineBuilding = 0
      allParams.calcParams.windTurbineOthers = 0
    }
    if (!gridShow) {
      allParams.calcParams.powerToHydrogenRatio = 1

      allParams.calcParams.capfirstYearUpGridPower = 0
      allParams.calcParams.capfirstYearGridHydrogenProduction = 0
      allParams.calcParams.firstYearDownGridPower = 0
    }
    if (!batShow) {
      allParams.calcParams.batCapacity = 0
      allParams.calcParams.energyStorageEquipment = 0
      allParams.calcParams.energyStorageInstallation = 0
      allParams.calcParams.energyStorageBuilding = 0
      allParams.calcParams.energyStorageOthers = 0

    }
    if (!alkShow) {
      allParams.calcParams.alkCapacity = 0
      allParams.calcParams.electrolyzerEquipment = 0
      allParams.calcParams.electrolyzerInstallation = 0
      allParams.calcParams.electrolyzerBuilding = 0
      allParams.calcParams.electrolyzerOthers = 0
    }
    if (!alkStoreShow) {
      allParams.calcParams.alkStoreCapacity = 0
      allParams.calcParams.hydrogenStorageEquipment = 0
      allParams.calcParams.hydrogenStorageInstallation = 0
      allParams.calcParams.hydrogenStorageBuilding = 0
      allParams.calcParams.hydrogenStorageOthers = 0
    }

    const {code, data, msg} = await submitEconomicTask(allParams)
    if (code === 0) {
      message.success('经济分析完成')
      router.push({ name: 'economicAnalysis', query: { projectId: data.projectId, solutionId: data.solutionId } })
    } else {
      message.error(msg)
      currentStep.value = 3
    }
  } catch (error) {
    console.log('分析失败:', error)
    message.error('分析失败，请重试')
    // 分析失败时回到第3步
    currentStep.value = 3
  } finally {
    state.submitting = false
  }
}

const getCapParams = async () => {
  state.loading = true
  const {code, data, msg} = await getSolution({
    solutionId: route.query.capSolutionId,
    projectId: route.query.capProjectId
  })

  const { code: c, data: d, msg: m } = await getResult({
    solutionId: route.query.capSolutionId,
    projectId: route.query.capProjectId
  })
  // const allGreenPower = (d.gen_quantity || 0) / 10 // 绿电总发电量
  // const greenPowerH2 =  (d.ge_quantity || 0) / 10 // 绿电制氢用电占比
  // const greenPowerPublicLoad = 0 // 绿电其他负荷用电

  if (code === 0) {
    capParams.value = {
      projectName: data.project.name,
      customerName: data.project.customer,
      desc: data.project.desc,
      operatingYears: data.project.cycle,
      loanTerm: data.project.loanCycle,
      baseLoanRate: data.project.loanRate,
      financingRatioBase: data.project.loanRadio,
      projectScene: data.solution.topology,
      waterPriceNoTax: data.calcParams.water_price,
      // data.calcParams.grid_sale_price 绿电上网价格(元/kwh)

      // EPC 相关字段从默认值中获取，不覆盖用户输入
      // pvEPC: data.calcParams.pv_epc,
      // windEPC: data.calcParams.wind_epc,
      // batEPC: data.calcParams.es_epc,
      // alkEPC: d.h2_capex / d.ele_capacity / 100 + 1,
      // alkStoreEPC: data.calcParams.hs_invest,

      pvCapacity: d.pv_capacity || 0,
      windCapacity: d.wind_capacity || 0,
      batCapacity: d.es_capacity || 0,
      alkCapacity: d.ele_capacity || 0,
      alkStoreCapacity: d.hs_volume || 0, // m³
      // firstyear
      // capfirstYearGreenPower: (d.gen_quantity || 0) / 10, 
      // capfirstYearHydrogenElectricity: (d.ge_quantity || 0) / 10,
      // capfirstYearUpGridPower: (d.grid_up_quantity || 0) / 10, 
      // capfirstYearHydrogenProduction: (d.h2_ge_quantity || 0) / 10, 
      // capfirstYearGridHydrogenProduction: (d.h2_grid_quantity || 0) / 10, 
      // firstYearDownGridPower: (d.grid_down_quantity || 0) / 10,

      greenPowerH2Ratio: (d.ge_quantity || 0) / d.gen_quantity, // 绿电制氢用电占比
      greenPowerPublicLoadRatio: 0, // TODO: 绿电其他负荷用电占比
      greenPowerUpGridRatio: (d.grid_up_quantity || 0) / d.gen_quantity, // 绿电上网占比
      greenPowerDeprecatedRatio: d.abort_radio || 0, // 绿电弃电占比

      firstYearDownGridH2Power: (d.grid_down_quantity || 0) / 10, // 下网制氢用电
      firstYearDownGridPublicLoadPower: 0, // TODO:下网其他负荷用电

      pvFirstYearPowerGenerationHour: d.pv_gen_hours || 0, // pv首年发电小时数
      windFirstYearPowerGenerationHour: d.wind_gen_hours || 0, // wind首年发电小时数
      electrolyzerPowerConsumption: ((d.pv_capacity || 0) * (d.pv_gen_hours || 0) + (d.wind_capacity || 0) * (d.wind_gen_hours || 0)) * 1000 / (d.h2_ge_quantity * 1000 / 0.08988), // 电解槽电耗

      isFromCapacity: true
    }

    formState.value = {
      ...formState.value,
      ...capParams.value,
    }
    state.loading = false
    
     // 自动计算各模块的投资费用
    //  autoCalculateInvestmentFromCapacity()
     
     console.log('capParams api:', capParams.value, formState.value)
  } else {
    console.error('Cap getResult', msg)
  }
 }

// 通用的投资费用计算函数
const calculateModuleInvestment = (module, section, formData) => {
  if (section !== 'investment') return
  
  // 模块配置映射
  const moduleConfig = {
    pv: {
      capacityKey: 'pvCapacity',
      epcKey: 'pvEPC',
      ratioPrefix: 'pv',
      outputPrefix: 'photovoltaic'
    },
    wind: {
      capacityKey: 'windCapacity',
      epcKey: 'windEPC',
      ratioPrefix: 'wind',
      outputPrefix: 'windTurbine'
    },
    storage: {
      capacityKey: 'batCapacity',
      epcKey: 'batEPC',
      ratioPrefix: 'bat',
      outputPrefix: 'energyStorage'
    },
    hydrogen: {
      capacityKey: 'alkCapacity',
      epcKey: 'alkEPC',
      ratioPrefix: 'alk',
      outputPrefix: 'electrolyzer'
    },
    hydrogenStorage: {
      capacityKey: 'alkStoreCapacity',
      epcKey: 'alkStoreEPC',
      ratioPrefix: 'alkStore',
      outputPrefix: 'hydrogenStorage'
    }
  }
  
  const config = moduleConfig[module]
  if (!config) return
  
  // 获取数据源：优先使用 formData，其次使用 formState.value
  // 注意：这里使用 ?? 0 来处理 undefined/null，但允许 0 值参与计算
  const capacity = formData[config.capacityKey] ?? formState.value[config.capacityKey] ?? 0
  const epc = formData[config.epcKey] ?? formState.value[config.epcKey] ?? 0
  
  // 修改条件判断：只有当 epc 不存在时才返回，容量为0时也要计算（结果为0）
  if (epc === undefined || epc === null) return
  
  // 获取占比：优先使用 formData，其次使用 formState.value，最后使用 initValue.js 中的默认值
  const getRatio = (type) => {
    let ratioKey
    if (type === 'equipment') {
      ratioKey = `${config.ratioPrefix}EquipmentRatio`
    } else if (type === 'install') {
      ratioKey = `${config.ratioPrefix}InstallRatio`
    } else if (type === 'building') {
      ratioKey = `${config.ratioPrefix}BuildingRatio`
    } else if (type === 'others') {
      ratioKey = `${config.ratioPrefix}OthersRatio`
    }
    
    const value = formData[ratioKey] || formState.value[ratioKey] || economicFormDefaultVal[ratioKey]
    console.log(`获取${module}模块${type}占比:`, { ratioKey, value })
    return value
  }
  
  // 统一计算逻辑：即使容量为0，也正常计算（结果为0）
  const baseAmount =  module === 'hydrogenStorage' ?
    Decimal(capacity || 0).mul(Decimal(epc || 0)).div(10000) :
    Decimal(capacity || 0).mul(Decimal(epc || 0)).mul(100)
  
  // 计算并更新投资费用字段
  formState.value[`${config.outputPrefix}Equipment`] = baseAmount.mul(Decimal(getRatio('equipment'))).toNumber()
  formState.value[`${config.outputPrefix}Installation`] = baseAmount.mul(Decimal(getRatio('install'))).toNumber()
  formState.value[`${config.outputPrefix}Building`] = baseAmount.mul(Decimal(getRatio('building'))).toNumber()
  formState.value[`${config.outputPrefix}Others`] = baseAmount.mul(Decimal(getRatio('others'))).toNumber()
}

// 自动计算来自容量系统的投资费用
const autoCalculateInvestmentFromCapacity = () => {
  // 使用默认占比自动计算各模块投资费用
  calculateModuleInvestment('pv', 'investment', {})
  calculateModuleInvestment('wind', 'investment', {})
  calculateModuleInvestment('storage', 'investment', {})
  calculateModuleInvestment('hydrogen', 'investment', {})
  calculateModuleInvestment('hydrogenStorage', 'investment', {})
}
 
 // 检查项目名称是否已存在，如果存在则设置项目ID（主要用于容量系统过来的情况）
const checkAndSetProjectId = (projectName) => {
  if (!projectName || !ecoProjectList.value.length) return
  
  const existingProject = ecoProjectList.value.find(p => p.name === projectName)
  if (existingProject) {
    selectedProjectId.value = existingProject.id
    console.log(`项目 "${projectName}" 已存在，设置项目ID: ${existingProject.id}`)
  } else {
    selectedProjectId.value = undefined
    console.log(`项目 "${projectName}" 不存在，认为是新项目`)
  }
}

// 初始化表单数据
const initFormData = async () => {
  // 先设置默认值
  formState.value = { ...economicFormDefaultVal }
  
  // 如果URL中有projectId和solutionId，使用已有方案的参数（编辑已有方案）
  if (route.query.projectId && route.query.solutionId) {
    try {
      state.loading = true
      await getSolutionParams()
      // 将获取到的方案参数合并到表单中
      if (Object.keys(solutionParams.value).length > 0) {
        formState.value = { ...formState.value, ...solutionParams.value }
        console.log('使用已有方案参数:', solutionParams.value)
        
        // 编辑已有方案时，直接使用URL中的projectId
        selectedProjectId.value = parseInt(route.query.projectId)
        console.log(`编辑已有方案，直接使用项目ID: ${selectedProjectId.value}`)
      }
    } catch (error) {
      console.error('获取方案参数失败:', error)
      message.error('获取方案参数失败，使用默认值')
    } finally {
      state.loading = false
    }
  }

  if (route.query.capSolutionId && route.query.capProjectId) {
    await getCapParams()
    
    // 检查从容量系统获取的项目名称是否已存在
    if (formState.value.projectName) {
      checkAndSetProjectId(formState.value.projectName)
    }
    
    autoCalculateInvestmentFromCapacity()
  }
  
  // 处理项目场景数据转换：将[1,1,0,1,1,0]格式转换为['pv','wind','hydrogen']格式
  if (formState.value.projectScene && Array.isArray(formState.value.projectScene)) {
    state.loading = true

    const moduleOptions = [
      { value: 'pv', index: 0 },
      { value: 'wind', index: 1 },
      { value: 'grid', index: 2 },
      { value: 'storage', index: 3 },
      { value: 'hydrogen', index: 4 },
      { value: 'hydrogenStorage', index: 5 }
    ]
    
    // 如果是0,1数组格式，转换为选中值数组
    if (formState.value.projectScene.length === 6 && 
        typeof formState.value.projectScene[0] === 'number') {
      const selectedModules = []
      moduleOptions.forEach(option => {
        if (formState.value.projectScene[option.index] === 1) {
          selectedModules.push(option.value)
        }
      })
      formState.value.projectScene = selectedModules
    }
    state.loading = false

  }
}

const getSolutionParams = async () => {
  const {code, data: { calcParams }, msg} = await getEconomicSolutionInfo({
    solutionId: route.query.solutionId,
    projectId: route.query.projectId
  })
  if (code === 0) {
    solutionParams.value = {
      ...calcParams,
    }
    console.log('solutionParams', solutionParams.value)
  } else {
    message.error(msg)
  }
}

// 监听项目场景变化，动态更新资源配置标签页
watch(() => formState.value.projectScene, (newVal) => {
  if (newVal !== undefined) {
    const activeKeys = getActiveResourceKeys()
    if (activeKeys.length > 0 && !activeKeys.includes(activeResourceKey.value)) {
      activeResourceKey.value = activeKeys[0]
    } else if (activeKeys.length === 0) {
      activeResourceKey.value = 'others'
    }
  }
})

onMounted(async () => {
  getEcoProjectList()
  await initFormData()

})
</script>

<style lang="less" scoped>
@import '@/style/base.less';

.body_wrap {
  font-size: 12px;
  padding: 10px 20px 80px 20px;
  position: relative;
  
  .steps_wrap {
    margin: 20px 0;
    
    .steps_header {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px 0;
      
      .step_item {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0 40px;
        position: relative;
        
        &:not(:last-child)::after {
          content: '';
          position: absolute;
          top: 15px;
          left: 50px;
          width: 80px;
          height: 2px;
          background-color: #d9d9d9;
          z-index: 1;
        }
        
        &.active::after,
        &.completed::after {
          background-color: @baseColor;
        }
        
        .step_circle {
          width: 30px;
          height: 30px;
          border-radius: 50%;
          background-color: #d9d9d9;
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          z-index: 2;
          position: relative;
        }
        
        .step_title {
          margin-top: 8px;
          font-size: 14px;
          color: #999;
        }
        
        &.active {
          .step_circle {
            background-color: @baseColor;
          }
          .step_title {
            color: @baseColor;
            font-weight: bold;
          }
        }
        
        &.completed {
          .step_circle {
            background-color: @baseColor;
          }
          .step_title {
            color: #333;
          }
        }
      }
    }
  }
  
  .content_wrap {
    .step_content {
      min-height: 400px;
    }
    
    .box_wrap {
      padding: 20px 20px 15px 20px;
      background-color: #fff;
      margin-bottom: 20px;
      
      .b_title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 20px;
      }
      
      .line_item {
        display: flex;
        flex-wrap: wrap;
        
        .line_form_item {
          width: 33.33%;
          margin-bottom: 15px;
          font-size: 12px;
          
          @media (max-width: 1400px) {
            width: 50%;
          }
          
          @media (max-width: 1000px) {
            width: 100%;
          }
          
          // 项目场景checkbox组特殊处理
          .ant-checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            
            .ant-checkbox-wrapper {
              margin-right: 0;
              margin-bottom: 8px;
              
              &:hover {
                .ant-checkbox-inner {
                  border-color: @baseColor;
                }
              }
            }
            
            .ant-checkbox-wrapper-checked {
              .ant-checkbox-inner {
                background-color: @baseColor;
                border-color: @baseColor;
              }
            }
          }
        }
      }
    }
    
    .resource_section {
      .resource_sub_section {
        margin-bottom: 30px;
        
        .sub_title {
          display: flex;
          align-items: center;
          margin-bottom: 15px;
          font-size: 14px;
          font-weight: bold;
          
          .auxiliary_icon,
          .extend_icon {
            margin-left: 8px;
            color: @baseColor;
            cursor: pointer;
            transition: all 0.3s ease;
            
            &:hover {
              color: @baseColor;
              transform: scale(1.1);
            }
          }
          
          .auxiliary_icon {
            // 计算器图标稍微大一点
            font-size: 16px;
          }
          
          .extend_icon {
            // 扩展图标保持默认大小
            font-size: 14px;
          }
          
          // 确保按钮本身的样式
          .ant-btn-link {
            padding: 0;
            height: auto;
            border: none;
            
            &:hover {
              background: transparent;
            }
          }
        }
      }
    }
  }
  
  .button_wrap {
    position: fixed;
    bottom: 15px;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    padding: 8px 20px;
    z-index: 100;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    transition: left 0.3s ease, width 0.3s ease;

    width: calc(100% - 50px);
    left: 0;
    transform: translateX(25px);
    
    .button_group {
      display: flex;
      justify-content: center;
      gap: 15px;
    }
  }
}

.input_deal_wrap {
  width: 100% !important;
}

.analysis_loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: #fff;
  
  .loading_content {
    text-align: center;
    
    .loading_icon {
      margin-bottom: 20px;
      
      .loading_circle {
        width: 60px;
        height: 60px;
        border: 4px solid #f0f0f0;
        border-top: 4px solid @baseColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto;
      }
    }
    
    .loading_text {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      margin-bottom: 10px;
    }
    
    .loading_desc {
      font-size: 14px;
      color: #666;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
