import{S as y,r as x,e as k,d as C,A as S,f as t,b as l,c as r,p as I,g as p,t as N,i as V,k as B,h as e,w as a,n as U,T as q,v as D,E,G as L,U as T,_ as A}from"./index-Dy1zyu0d.js";const F="/assets/login_logo-CZWDvsrr.png",f=n=>(E("data-v-062ce426"),n=n(),L(),n),G={class:"bg_wrap"},R={key:0},W=f(()=>p("img",{class:"logo_img",src:T},null,-1)),Z=[W],j={class:"login_window"},z={key:0,class:"title"},H=f(()=>p("img",{src:F,class:"img_wrap"},null,-1)),J=[H],K={key:1,class:"title"},M=y({__name:"index",setup(n){const c=x(!1),d=k(()=>!0),g=C(),o=S({username:"",password:""}),v=async m=>{c.value=!0;const{code:s,msg:i}=await q(m);c.value=!1,s===0?(console.log("login success"),g.push({name:"home"})):D.error(i)};return(m,s)=>{const i=t("a-input"),_=t("a-form-item"),w=t("a-input-password"),h=t("a-button"),b=t("a-form");return l(),r("div",G,[d.value?(l(),r("div",R,Z)):I("",!0),p("div",j,[d.value?(l(),r("div",z,J)):(l(),r("div",K,N(V(B).title),1)),e(b,{class:"form_wrap",model:o,name:"basic","label-col":{span:6},"wrapper-col":{span:14},autocomplete:"off",onFinish:v},{default:a(()=>[e(_,{label:"用户名",name:"username",rules:[{required:!0,message:"请输入用户名!"}]},{default:a(()=>[e(i,{value:o.username,"onUpdate:value":s[0]||(s[0]=u=>o.username=u)},null,8,["value"])]),_:1}),e(_,{label:"密码",name:"password",rules:[{required:!0,message:"请输入密码!"}]},{default:a(()=>[e(w,{value:o.password,"onUpdate:value":s[1]||(s[1]=u=>o.password=u)},null,8,["value"])]),_:1}),e(_,{"wrapper-col":{offset:6,span:14}},{default:a(()=>[e(h,{loading:c.value,style:{width:"100%"},type:"primary","html-type":"submit"},{default:a(()=>[U(" 登录 ")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])])])}}}),P=A(M,[["__scopeId","data-v-062ce426"]]);export{P as default};
