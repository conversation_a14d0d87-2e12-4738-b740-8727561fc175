import{g as x}from"./index-OFiCwQ0n.js";import{_ as v,r as u,o as h,f as i,b as t,c,g as m,h as a,w as e,F as I,l as w,m as C,n as o,t as z,p as B,E as N,G as S}from"./index-Dy1zyu0d.js";const V=s=>(N("data-v-496fa3b4"),s=s(),S(),s),A={class:"body_wrap"},D=V(()=>m("div",{class:"title1"}," 策略管理 ",-1)),E={class:"content_wrap"},F={key:0},L={key:1},T={__name:"index",setup(s){const _=u([]),d=u(!1),y=[{title:"名称",dataIndex:"name",key:"name"},{title:"状态",dataIndex:"status",key:"status"},{title:"描述",dataIndex:"desc",key:"desc"},{title:"周期",key:"period",dataIndex:"period"},{title:"评分",key:"score",dataIndex:"score"},{title:"定制时间",key:"createAt",dataIndex:"createAt"},{title:"定制人",key:"creator",dataIndex:"creator"},{title:"Action",key:"action"}],k=async()=>{d.value=!0;const{data:r}=await x();_.value=r,d.value=!1,console.log("table: ",r)};return h(()=>{k()}),(r,G)=>{const f=i("a-tag"),n=i("a-button"),g=i("a-table");return t(),c("div",A,[D,m("div",E,[a(g,{loading:d.value,columns:y,"data-source":_.value,pagination:!1},{bodyCell:e(({column:p,record:b})=>[p.key==="tags"?(t(),c("span",F,[(t(!0),c(I,null,w(b.tags,l=>(t(),C(f,{key:l,color:l==="loser"?"volcano":l.length>5?"geekblue":"green"},{default:e(()=>[o(z(l.toUpperCase()),1)]),_:2},1032,["color"]))),128))])):p.key==="action"?(t(),c("span",L,[a(n,{type:"link",size:"small"},{default:e(()=>[o("详情")]),_:1}),a(n,{type:"link",size:"small"},{default:e(()=>[o("暂停/启动")]),_:1}),a(n,{type:"link",size:"small"},{default:e(()=>[o("删除")]),_:1}),a(n,{type:"link",size:"small"},{default:e(()=>[o("配置")]),_:1})])):B("",!0)]),_:1},8,["loading","data-source"])])])}}},U=v(T,[["__scopeId","data-v-496fa3b4"]]);export{U as default};
