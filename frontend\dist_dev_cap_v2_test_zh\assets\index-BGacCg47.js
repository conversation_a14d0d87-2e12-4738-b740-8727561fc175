import{n as Pe,o as Ee,i as Me,u as Ae,p as ge,q as ye,d as Be,r as Ue}from"./index-D92dbiRQ.js";import{_ as ue,r as Y,A as Fe,e as de,H as X,o as pe,f as _,b as w,m as V,w as s,h as o,n as q,v as P,O as ae,B as Ie,c as U,g as p,i as te,P as he,t as ie,K as Le,p as J,F as re,l as ce,y as se,E as ve,G as _e,J as Ne,M as be,d as Ke}from"./index-Dy1zyu0d.js";import{g as xe}from"./index-DlWGaHj9.js";import{i as $e,l as fe}from"./lodash-SRgxwWxU.js";const we=()=>[{title:"厂商",dataIndex:"manufacturer",s_type:"string",rules:[{required:!0,message:"请输入厂商"}],unit:""},{title:"产品型号",dataIndex:"model",s_type:"string",rules:[{required:!0,message:"请输入产品型号"}],unit:""},{title:"衰减率曲线",dataIndex:"damp_curve",key:"damp_curve",width:200},{title:"操作",dataIndex:"action",key:"action"}],Oe=()=>[{title:"",dataIndex:""},{title:"操作",dataIndex:"action",key:"action"}],me=()=>[{title:"厂商",dataIndex:"manufacturer",s_type:"string",rules:[{required:!1}],unit:""},{title:"产品型号",dataIndex:"model",s_type:"string",rules:[{required:!1}],unit:""},{title:"单机功率(MW)",dataIndex:"single_power",s_type:"number",rules:[{required:!1}],unit:""},{title:"充电效率",dataIndex:"charge_efficiency",s_type:"number",rules:[{required:!1}],unit:""},{title:"放电效率",dataIndex:"discharge_efficiency",s_type:"number",rules:[{required:!1}],unit:""},{title:"充放电倍率",dataIndex:"c_rate",s_type:"number",rules:[{required:!1}],unit:""},{title:"初始SOC",dataIndex:"init_soc",s_type:"number",rules:[{required:!1}],unit:""},{title:"SOC下限",dataIndex:"min_soc",s_type:"number",rules:[{required:!1}],unit:""},{title:"SOC上限",dataIndex:"max_soc",s_type:"number",rules:[{required:!1}],unit:""},{title:"寿命(年)",dataIndex:"life_cycle",s_type:"number",rules:[{required:!1}],unit:""},{title:"操作",dataIndex:"action",key:"action"}],ke=()=>[{title:"厂商",dataIndex:"manufacturer",s_type:"string",rules:[{required:!1}],unit:""},{title:"产品型号",dataIndex:"model",s_type:"string",rules:[{required:!1}],unit:"",width:"80px"},{title:"类型",dataIndex:"ele_type",key:"ele_type",s_type:"select",rules:[{required:!1}],unit:"",options:[{label:"ALK",value:1,color:"blue"},{label:"PEM",value:2,color:"green"}]},{title:"容量(Nm³/h)",dataIndex:"capacity",s_type:"number",rules:[{required:!1}],unit:""},{title:"能耗(kwh/Nm³)",dataIndex:"power_consumption",s_type:"number",rules:[{required:!1}],unit:""},{title:"额定功率(MW)",dataIndex:"pe",s_type:"number",rules:[{required:!1}],unit:""},{title:"最低负载率",dataIndex:"lower_load_rate",s_type:"number",rules:[{required:!1}],unit:""},{title:"最高负载率",dataIndex:"upper_load_rate",s_type:"number",rules:[{required:!1}],unit:""},{title:"辅助系统能耗(kwh/Nm³)",dataIndex:"assist_consumption",s_type:"number",rules:[{required:!1}],unit:""},{title:"系统价格(元/套)",dataIndex:"price",s_type:"number",rules:[{required:!1}],unit:""},{title:"制氢电源效率",dataIndex:"power_supply_efficiency",s_type:"number",rules:[{required:!1}],unit:""},{title:"操作",dataIndex:"action",key:"action",width:"100px"}],Ce=()=>[{title:"厂商",dataIndex:"manufacturer",s_type:"string",rules:[{required:!1}],unit:""},{title:"产品型号",dataIndex:"model",s_type:"string",rules:[{required:!1}],unit:""},{title:"体积(m³)",dataIndex:"volume",s_type:"number",rules:[{required:!1}],unit:""},{title:"最小运行压力(Mpa)",dataIndex:"min_pressure",s_type:"number",rules:[{required:!1}],unit:""},{title:"最大运行压力(Mpa)",dataIndex:"max_pressure",s_type:"number",rules:[{required:!1}],unit:""},{title:"价格(元)",dataIndex:"price",s_type:"number",rules:[{required:!1}],unit:""},{title:"占地面积(㎡)",dataIndex:"area",s_type:"number",rules:[{required:!1}],unit:""},{title:"操作",dataIndex:"action",key:"action"}],Ve={__name:"RegionModal",props:{visible:Boolean,existingRegions:{type:Array,default:()=>[]}},emits:["update:visible","confirm"],setup(C,{emit:B}){const r=C,g=B,h=Y({}),b=Y(),a=Fe({regionType:"existing",province:"",city:"",customRegion:"",period:"year",cascaderValue:[],selectedZoneInfo:null}),R={regionType:[{required:!0,message:"请选择地区类型"}],province:[{required:!0,message:"请选择省份"}],city:[{required:!0,message:"请选择城市"}],customRegion:[{required:!0,message:"请输入地区名称"}],period:[{required:!0,message:"请选择电价区间"}]},T=de(()=>h.value.result.map(D=>({value:D.province,label:D.province,children:D.subZones.map(I=>({value:I.city,label:I.city,zoneId:I.id,region:I.region,country:I.country}))}))),S=()=>{a.province="",a.city="",a.customRegion="",a.cascaderValue=[],a.selectedZoneInfo=null},F=(D,I)=>{D&&D.length===2&&I&&I.length===2?(a.province=D[0],a.city=D[1],a.selectedZoneInfo={zoneId:I[1].zoneId,region:I[1].region,country:I[1].country}):(a.province="",a.city="",a.selectedZoneInfo=null)},z=async()=>{var D,I,e;try{await b.value.validate();let c={period:a.period};if(a.regionType==="existing"){if(r.existingRegions.some(f=>f.province===a.province&&f.city===a.city)){P.error("该地区已存在，不能重复添加");return}c={...c,zoneId:((D=a.selectedZoneInfo)==null?void 0:D.zoneId)||Date.now(),region:((I=a.selectedZoneInfo)==null?void 0:I.region)||"",country:((e=a.selectedZoneInfo)==null?void 0:e.country)||"中国",province:a.province,city:a.city,groupedTouPriceByMonth:[]}}else{if(r.existingRegions.some(f=>f.province===a.customRegion&&!f.city)){P.error("该自定义地区已存在，不能重复添加");return}c={...c,zoneId:null,region:"自定义地区",country:"中国",province:a.customRegion,city:"",groupedTouPriceByMonth:[]}}g("confirm",c),E()}catch(c){console.log("表单验证失败:",c)}},E=()=>{var D;g("update:visible",!1),(D=b.value)==null||D.resetFields(),Object.assign(a,{regionType:"existing",province:"",city:"",customRegion:"",period:"year",cascaderValue:[],selectedZoneInfo:null})},M=async()=>{const{code:D,msg:I,data:e}=await Pe();D===0?h.value=e:P.error(I)};return X(()=>r.visible,D=>{D||E()}),pe(async()=>{M()}),(D,I)=>{const e=_("a-radio"),c=_("a-radio-group"),d=_("a-form-item"),f=_("a-cascader"),A=_("a-input"),K=_("a-select-option"),H=_("a-select"),x=_("a-form"),y=_("a-modal");return w(),V(y,{visible:C.visible,title:"新增地区",width:500,onOk:z,onCancel:E},{default:s(()=>[o(x,{ref_key:"formRef",ref:b,model:a,rules:R,"label-col":{span:6},"wrapper-col":{span:18}},{default:s(()=>[o(d,{label:"地区类型",name:"regionType"},{default:s(()=>[o(c,{value:a.regionType,"onUpdate:value":I[0]||(I[0]=k=>a.regionType=k),onChange:S},{default:s(()=>[o(e,{value:"existing"},{default:s(()=>[q("选择已有地区")]),_:1}),o(e,{value:"custom"},{default:s(()=>[q("自定义地区")]),_:1})]),_:1},8,["value"])]),_:1}),a.regionType==="existing"?(w(),V(d,{key:0,label:"地区选择",name:"province"},{default:s(()=>[o(f,{value:a.cascaderValue,"onUpdate:value":I[1]||(I[1]=k=>a.cascaderValue=k),options:T.value,placeholder:"请选择省份/城市","show-search":!0,onChange:F},null,8,["value","options"])]),_:1})):(w(),V(d,{key:1,label:"地区名称",name:"customRegion"},{default:s(()=>[o(A,{value:a.customRegion,"onUpdate:value":I[2]||(I[2]=k=>a.customRegion=k),placeholder:"请输入自定义地区名称"},null,8,["value"])]),_:1})),o(d,{label:"电价区间",name:"period"},{default:s(()=>[o(H,{value:a.period,"onUpdate:value":I[3]||(I[3]=k=>a.period=k),placeholder:"请选择电价区间"},{default:s(()=>[o(K,{value:"year"},{default:s(()=>[q("年度(整年)")]),_:1}),o(K,{value:"halfYear"},{default:s(()=>[q("半年(上下半年)")]),_:1}),o(K,{value:"quarter"},{default:s(()=>[q("季度(四个季度)")]),_:1}),o(K,{value:"month"},{default:s(()=>[q("月度(12个月)")]),_:1})]),_:1},8,["value"])]),_:1})]),_:1},8,["model"])]),_:1},8,["visible"])}}},Ze=ue(Ve,[["__scopeId","data-v-6d2503c9"]]),je={__name:"PriceChart",props:{data:{type:Array,default:()=>[]}},emits:["select"],setup(C,{expose:B,emit:r}){const g=C,h=r,b=Y();let a=null;const R={1:"#52c41a",2:"#1890ff",3:"#faad14",4:"#ff7a45",5:"#f5222d"},T={1:"深谷",2:"低谷",3:"平时",4:"高峰",5:"尖峰"},S=()=>{a&&a.resize()},F=()=>{b.value&&(a=$e(b.value),a.on("click",E=>{h("select",{hour:E.dataIndex,data:E.data})}),z())},z=()=>{if(!a)return;const E=Array.from({length:24},(e,c)=>c),M={};Object.keys(T).forEach(e=>{M[e]={name:T[e],data:new Array(24).fill(null),color:R[e]}}),g.data.forEach(e=>{for(let c=e.begin;c<=e.end;c++)M[e.type].data[c]=e.price});const D=Object.keys(M).filter(e=>M[e].data.some(c=>c!==null)).map(e=>({name:M[e].name,type:"line",data:M[e].data,step:"end",lineStyle:{width:3,color:M[e].color},areaStyle:{color:M[e].color+"30"},symbol:"circle",symbolSize:6,itemStyle:{color:M[e].color},connectNulls:!1,z:parseInt(e)})),I={title:{left:"center",textStyle:{fontSize:16,fontWeight:"normal"}},tooltip:{trigger:"axis",formatter:e=>{const c=e[0].dataIndex,d=g.data.find(K=>c>=K.begin&&c<=K.end),f=e.find(K=>K.value!==null&&K.value!==void 0);let A=0;return f&&f.value!==null&&f.value!==void 0?A=f.value:d&&d.price!==null&&d.price!==void 0&&(A=d.price),`
          <div>
            <div>时间: ${c}:00-${c+1}:00</div>
            <div>类型: ${d?T[d.type]:"未配置"}</div>
            <div>价格: ${A} 元/kWh</div>
          </div>
        `}},legend:{show:!0,top:"8%",data:D.map(e=>({name:e.name,icon:"circle",textStyle:{color:e.lineStyle.color}}))},grid:{top:"20%",left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:E.map(e=>`${e}:00`),axisLabel:{interval:1,rotate:45},boundaryGap:!1},yAxis:{type:"value",name:"价格(元/kWh)",min:0},series:D};a.setOption(I,!0)};return X(()=>g.data,()=>{ae(()=>{z()})},{deep:!0}),pe(()=>{ae(()=>{F()}),window.addEventListener("resize",S)}),Ie(()=>{window.removeEventListener("resize",S),a&&(a.dispose(),a=null)}),B({updateChart:z,resize:S}),(E,M)=>(w(),U("div",{class:"price-chart",ref_key:"chartRef",ref:b},null,512))}},He=ue(je,[["__scopeId","data-v-c93ed15e"]]),Se=C=>(ve("data-v-811ee9f3"),C=C(),_e(),C),Ge={class:"grid-price-config"},We={class:"main-content"},Qe={class:"left-panel"},Je={class:"panel-header"},Xe=Se(()=>p("span",{class:"panel-title"},"地区",-1)),et={class:"region-tree"},tt={class:"tree-node-content"},at={class:"node-title"},nt={key:0,class:"node-actions"},lt={class:"right-panel"},ot={key:0,class:"config-area"},st={class:"config-header"},it={style:{display:"flex","justify-content":"space-between","align-items":"center"}},rt={class:"month-tabs"},ut={class:"price-config-section"},dt={class:"section-header"},ct=Se(()=>p("h4",null,"24小时电价配置",-1)),pt={class:"price-chart-container"},ft={key:1,class:"empty-state"},mt={class:"time-edit-content"},vt={class:"time-periods"},_t={class:"period-title"},gt={__name:"index",setup(C){const B=Y(!1),r=Y(!1),g=Y([]),h=Y([]),b=Y([]),a=Y(0),R=Y([]),T=Y();Y(!1);const S=de(()=>{if(h.value.length===0)return null;const t=h.value[0];return g.value.find(n=>n.id.toString()===t)}),F=de(()=>{const t=new Map,n=[];return g.value.forEach(u=>{const{province:i,city:m,id:N}=u;m?(t.has(i)||t.set(i,{title:i,key:`province-${i}`,children:[],selectable:!1}),t.get(i).children.push({title:m,key:N.toString(),isLeaf:!0,province:i,city:m})):n.push({title:i,key:N.toString(),isLeaf:!0,province:i,city:""})}),[...Array.from(t.values()),...n]}),z=de(()=>{if(!S.value)return[];const{period:t,groupedTouPriceByMonth:n}=S.value;let l=[];switch(t){case"year":l=[{label:"全年(1-12月)",months:[1,2,3,4,5,6,7,8,9,10,11,12],begin:1,end:12}];break;case"halfYear":l=[{label:"上半年(1-6月)",months:[1,2,3,4,5,6],begin:1,end:6},{label:"下半年(7-12月)",months:[7,8,9,10,11,12],begin:7,end:12}];break;case"quarter":l=[{label:"第一季度(1-3月)",months:[1,2,3],begin:1,end:3},{label:"第二季度(4-6月)",months:[4,5,6],begin:4,end:6},{label:"第三季度(7-9月)",months:[7,8,9],begin:7,end:9},{label:"第四季度(10-12月)",months:[10,11,12],begin:10,end:12}];break;case"month":l=Array.from({length:12},(u,i)=>({label:`${i+1}月`,months:[i+1],begin:i+1,end:i+1}));break;default:return[]}return l.map(u=>{const i=n==null?void 0:n.find(j=>j.begin===u.begin&&j.end===u.end),N=((i==null?void 0:i.groupedTouPriceByDay)||[]).map(j=>({type:j.type||3,price:j.price||0,begin:j.begin||0,end:j.end||23}));return{...u,priceData:N,hasData:N.length>0}})}),E=t=>{if(h.value=t,a.value=0,t.length>0){const n=g.value.find(l=>l.id.toString()===t[0]);console.log("选中的地区配置:",n),n&&(console.log("电价数据:",n.groupedTouPriceByMonth),console.log("推断的period类型:",n.period))}},M=t=>{b.value=t},D=t=>{const n=Date.now(),l={...t,id:n,zoneId:t.zoneId,currentYear:new Date().getFullYear().toString(),createTime:Date.now(),isNew:!0};g.value.push(l),h.value=[l.id.toString()],B.value=!1,t.city&&(b.value=[...new Set([...b.value,`province-${t.province}`])]),P.success("地区添加成功，请配置电价后保存")},I=async t=>{const{code:n,data:l,msg:u}=await Ee([parseInt(t)]);n===0?(P.success("删除成功"),await k()):P.error(u)},e=t=>{a.value=t},c=t=>{console.log("选中图表数据:",t)},d=()=>{const t=z.value[a.value];t&&t.priceData&&t.priceData.length>0?R.value=t.priceData.map(n=>({...n,timeRange:[se().hour(n.begin).minute(0),se().hour(n.end).minute(0)]})):R.value=[{type:3,price:.5,begin:0,end:23,timeRange:[se().hour(0).minute(0),se().hour(23).minute(0)]}],r.value=!0},f=()=>{const t=new Set;R.value.forEach(u=>{for(let i=u.begin;i<=u.end;i++)t.add(i)});let n=0;for(;t.has(n)&&n<24;)n++;let l=n;for(;!t.has(l+1)&&l<23;)l++;if(n>=24){P.warning("24小时时段已全部配置完成");return}R.value.push({type:3,price:.5,begin:n,end:l,timeRange:[se().hour(n).minute(0),se().hour(l).minute(0)]})},A=t=>{R.value.splice(t,1)},K=(t,n)=>{if(!n||n.length!==2)return;const l=n[0].hour(),u=n[1].hour();if(l>=u){P.error("开始时间必须小于结束时间");return}if(R.value.some((m,N)=>N===t?!1:!(u<m.begin||l>m.end))){P.error("时间段与其他时段冲突，请重新选择");return}R.value[t].begin=l,R.value[t].end=u},H=()=>{const t=z.value[a.value];t&&(t.priceData=R.value.map(n=>({begin:n.begin,end:n.end,price:n.price,type:n.type})),ae(()=>{T.value&&T.value.updateChart()}))};X(R,()=>{H()},{deep:!0});const x=async()=>{const t=new Set;R.value.forEach(l=>{for(let u=l.begin;u<=l.end;u++)t.add(u)});const n=[];for(let l=0;l<24;l++)t.has(l)||n.push(l);if(n.length>0){P.warning(`以下时段未配置: ${n.map(l=>l+":00").join(", ")}`);return}if(S.value){const l=z.value[a.value];if(l){const u=R.value.map(m=>({begin:m.begin,end:m.end,price:m.price,type:m.type})),i=g.value.findIndex(m=>m.id===S.value.id);if(i>-1){const m=g.value[i];m.groupedTouPriceByMonth||(m.groupedTouPriceByMonth=[]);const N=m.groupedTouPriceByMonth.findIndex(v=>v.begin===l.begin&&v.end===l.end);if(N>-1?m.groupedTouPriceByMonth[N].groupedTouPriceByDay=u:m.groupedTouPriceByMonth.push({begin:l.begin,end:l.end,groupedTouPriceByDay:u}),g.value[i]={...m},ee(m)){if(!await L(m))return;m.isNew&&(delete m.isNew,g.value[i]={...m}),P.success("电价配置保存成功")}else P.success("当前时段配置保存成功，请继续配置其他时段")}}}ae(()=>{T.value&&T.value.updateChart()}),r.value=!1},y=t=>{const n=[];return R.value.forEach((l,u)=>{if(u!==t)for(let i=l.begin;i<=l.end;i++)n.push(i)}),n};X(F,t=>{const n=[],l=u=>{u.forEach(i=>{i.isLeaf||n.push(i.key),i.children&&l(i.children)})};l(t),b.value=n,h.value.length===0&&g.value.length>0&&(h.value=[g.value[0].id.toString()],console.log("树形数据更新后默认选中第一个地区:",g.value[0]))},{immediate:!0});const k=async()=>{try{const{code:t,msg:n,data:l}=await Me({});t===0?(g.value=l.result.map(u=>{const{zone:i,price:m}=u,N=Object.keys(m),j=N.length>0?N[0]:new Date().getFullYear().toString(),v=m[j]||[];return{id:i.id,zoneId:i.id,region:i.region,country:i.country,province:i.province,city:i.city,currentYear:j,period:Z(v),createTime:Date.now(),groupedTouPriceByMonth:v.map($=>({begin:$.begin,end:$.end,groupedTouPriceByDay:$.groupedTouPriceByDay}))}}),console.log("转换后的数据:",g.value),g.value.length>0&&(h.value=[g.value[0].id.toString()],console.log("默认选中第一个地区:",g.value[0]))):P.error(n)}catch(t){console.error("获取电价配置失败:",t),P.error("获取电价配置失败")}},L=async t=>{try{const n=t.currentYear||new Date().getFullYear().toString(),l={zone:{id:t.zoneId,region:t.region,country:t.country,province:t.province,city:t.city},price:{[n]:t.groupedTouPriceByMonth.map(m=>({begin:m.begin,end:m.end,groupedTouPriceByDay:m.groupedTouPriceByDay}))}};console.log("提交的数据:",l);const{code:u,msg:i}=await Ae(l);return u===0?(P.success("电价配置更新成功"),!0):(P.error(i||"更新失败"),!1)}catch(n){return console.error("更新电价配置失败:",n),P.error("更新失败"),!1}},Z=t=>{if(!t||t.length===0)return"quarter";if(t.length===1){const n=t[0];if(n.begin===1&&n.end===12)return"year"}if(t.length===2){const n=t.some(u=>u.begin===1&&u.end===6),l=t.some(u=>u.begin===7&&u.end===12);if(n&&l)return"halfYear"}return t.length===4&&[{begin:1,end:3},{begin:4,end:6},{begin:7,end:9},{begin:10,end:12}].every(u=>t.some(i=>i.begin===u.begin&&i.end===u.end))?"quarter":t.length===12&&Array.from({length:12},(l,u)=>u+1).every(l=>t.some(u=>u.begin===l&&u.end===l))?"month":"quarter"},ee=t=>{if(!t||!t.period)return!1;const{period:n,groupedTouPriceByMonth:l}=t;let u=[];switch(n){case"year":u=[{begin:1,end:12}];break;case"halfYear":u=[{begin:1,end:6},{begin:7,end:12}];break;case"quarter":u=[{begin:1,end:3},{begin:4,end:6},{begin:7,end:9},{begin:10,end:12}];break;case"month":u=Array.from({length:12},(i,m)=>({begin:m+1,end:m+1}));break;default:return!1}return u.every(i=>{const m=l==null?void 0:l.find(N=>N.begin===i.begin&&N.end===i.end);return m&&m.groupedTouPriceByDay&&m.groupedTouPriceByDay.length>0})};return pe(async()=>{await k(),g.value.length===0&&console.log("没有获取到数据，添加测试数据")}),(t,n)=>{const l=_("a-button"),u=_("a-popconfirm"),i=_("a-tree"),m=_("a-tag"),N=_("a-tab-pane"),j=_("a-tabs"),v=_("a-empty"),$=_("a-select-option"),G=_("a-select"),le=_("a-form-item"),oe=_("a-input-number"),Re=_("a-time-range-picker"),Ye=_("a-form"),ze=_("a-card"),qe=_("a-space"),Te=_("a-drawer");return w(),U("div",Ge,[p("div",We,[p("div",Qe,[p("div",Je,[Xe,o(l,{type:"primary",size:"small",onClick:n[0]||(n[0]=O=>B.value=!0)},{default:s(()=>[o(te(he)),q(" 新增 ")]),_:1})]),p("div",et,[o(i,{"tree-data":F.value,"selected-keys":h.value,"expanded-keys":b.value,"default-expand-all":!0,onSelect:E,onExpand:M},{title:s(({title:O,key:Q,isLeaf:W})=>[p("div",tt,[p("span",at,ie(O),1),W?(w(),U("div",nt,[o(u,{title:"确定删除此地区配置吗？",onConfirm:la=>I(Q),onClick:n[1]||(n[1]=Le(()=>{},["stop"]))},{default:s(()=>[o(l,{size:"small",type:"link",danger:""},{default:s(()=>[q("删除")]),_:1})]),_:2},1032,["onConfirm"])])):J("",!0)])]),_:1},8,["tree-data","selected-keys","expanded-keys"])])]),p("div",lt,[S.value?(w(),U("div",ot,[p("div",st,[p("div",it,[p("h3",null,[q(ie(S.value.province)+ie(S.value.city?"-"+S.value.city:"")+" ",1),S.value.isNew?(w(),V(m,{key:0,color:"orange",style:{"margin-left":"8px"}},{default:s(()=>[q(" 未保存 ")]),_:1})):J("",!0)])])]),p("div",rt,[o(j,{activeKey:a.value,"onUpdate:activeKey":n[2]||(n[2]=O=>a.value=O),onChange:e},{default:s(()=>[(w(!0),U(re,null,ce(z.value,(O,Q)=>(w(),V(N,{key:Q,tab:O.label},{default:s(()=>[p("div",ut,[p("div",dt,[ct,o(l,{type:"primary",size:"small",onClick:d},{default:s(()=>[q(" 编辑 ")]),_:1})]),p("div",pt,[o(He,{ref_for:!0,ref_key:"priceChartRef",ref:T,data:O.priceData,onSelect:c},null,8,["data"])])])]),_:2},1032,["tab"]))),128))]),_:1},8,["activeKey"])])])):(w(),U("div",ft,[o(v,{description:"请选择左侧地区查看电价配置"})]))])]),o(Ze,{visible:B.value,"onUpdate:visible":n[3]||(n[3]=O=>B.value=O),"existing-regions":g.value,onConfirm:D},null,8,["visible","existing-regions"]),o(Te,{title:"电价时段编辑",visible:r.value,width:500,onClose:n[5]||(n[5]=O=>r.value=!1)},{footer:s(()=>[o(qe,null,{default:s(()=>[o(l,{onClick:n[4]||(n[4]=O=>r.value=!1)},{default:s(()=>[q("取消")]),_:1}),o(l,{type:"primary",onClick:x},{default:s(()=>[q("确认")]),_:1})]),_:1})]),default:s(()=>[p("div",mt,[p("div",vt,[(w(!0),U(re,null,ce(R.value,(O,Q)=>(w(),U("div",{key:Q,class:"period-item"},[o(ze,{size:"small"},{title:s(()=>[p("div",_t,[p("span",null,"时段 "+ie(Q+1),1),o(l,{size:"small",danger:"",type:"text",onClick:W=>A(Q)},{default:s(()=>[q(" 删除 ")]),_:2},1032,["onClick"])])]),default:s(()=>[o(Ye,{"label-col":{span:8},"wrapper-col":{span:16}},{default:s(()=>[o(le,{label:"类型"},{default:s(()=>[o(G,{value:O.type,"onUpdate:value":W=>O.type=W,onChange:H},{default:s(()=>[o($,{value:5},{default:s(()=>[q("尖峰")]),_:1}),o($,{value:4},{default:s(()=>[q("高峰")]),_:1}),o($,{value:3},{default:s(()=>[q("平时")]),_:1}),o($,{value:2},{default:s(()=>[q("低谷")]),_:1}),o($,{value:1},{default:s(()=>[q("深谷")]),_:1})]),_:2},1032,["value","onUpdate:value"])]),_:2},1024),o(le,{label:"价格"},{default:s(()=>[o(oe,{value:O.price,"onUpdate:value":W=>O.price=W,min:0,step:.01,style:{width:"100%"},"addon-after":"元/kWh",onChange:H},null,8,["value","onUpdate:value"])]),_:2},1024),o(le,{label:"时间段"},{default:s(()=>[o(Re,{value:O.timeRange,"onUpdate:value":W=>O.timeRange=W,format:"HH:00","minute-step":60,"hour-step":1,"disabled-hours":()=>y(Q),onChange:W=>K(Q,W)},null,8,["value","onUpdate:value","disabled-hours","onChange"])]),_:2},1024)]),_:2},1024)]),_:2},1024)]))),128))]),o(l,{type:"dashed",block:"",onClick:f,style:{"margin-top":"16px"}},{default:s(()=>[o(te(he)),q(" 新增时段 ")]),_:1})])]),_:1},8,["visible"])])}}},yt=ue(gt,[["__scopeId","data-v-811ee9f3"]]),ne=C=>(ve("data-v-0c60fb43"),C=C(),_e(),C),ht={class:"pv-device-editor"},bt={class:"damp-curve-section"},xt={class:"periods-editor"},wt={class:"editor-header"},kt=ne(()=>p("span",{class:"section-title"},"时间段配置",-1)),Ct={class:"periods-list"},It=ne(()=>p("div",{class:"period-label"},"衰减率",-1)),$t={class:"period-inputs"},St=ne(()=>p("span",{class:"year-label"},"第",-1)),Dt=ne(()=>p("span",{class:"year-label"},"年",-1)),Rt=ne(()=>p("span",{class:"separator"},"至",-1)),Yt=ne(()=>p("span",{class:"year-label"},"第",-1)),zt=ne(()=>p("span",{class:"year-label"},"年",-1)),qt={__name:"PvDeviceEditor",props:{modelValue:{type:Object,default:()=>({manufacturer:"",model:"",dampCurve:[]})},disabled:{type:Boolean,default:!1}},emits:["update:modelValue","change"],setup(C,{expose:B,emit:r}){const g=C,h=Y(),b=Y({manufacturer:"",model:""}),a=Y([{startYear:1,endYear:25,dampRate:.95}]),R=de(()=>{if(a.value.length===0)return[];const e=Math.max(...a.value.map(d=>d.endYear||0),25),c=new Array(e).fill(0);return a.value.forEach(d=>{if(d.startYear&&d.endYear&&d.dampRate!==null)for(let f=d.startYear;f<=d.endYear;f++)f<=e&&f>=1&&(c[f-1]=d.dampRate)}),c}),T={manufacturer:[{required:!0,message:"请输入厂商名称",trigger:"blur"}],model:[]},S=()=>{const e=a.value[a.value.length-1],c=e?e.endYear+1:1;a.value.push({startYear:c,endYear:c+1,dampRate:.95})},F=e=>{if(a.value.length<=1){P.warning("至少需要保留一个时间段");return}a.value.splice(e,1)},z=()=>{const e=[];for(let d=0;d<a.value.length;d++){const f=a.value[d];(!f.startYear||f.startYear<1)&&e.push(`时段${d+1}：开始年份无效`),(!f.endYear||f.endYear<1)&&e.push(`时段${d+1}：结束年份无效`),(f.dampRate===null||f.dampRate===void 0||f.dampRate<0||f.dampRate>1)&&e.push(`时段${d+1}：衰减率必须在0-1之间`),f.startYear&&f.endYear&&f.startYear>f.endYear&&e.push(`时段${d+1}：开始年份不能大于结束年份`)}if(e.length>0)return e;const c=[...a.value].sort((d,f)=>d.startYear-f.startYear);c[0].startYear!==1&&e.push("时间段必须从第1年开始");for(let d=0;d<c.length-1;d++){const f=c[d],A=c[d+1];f.endYear+1!==A.startYear&&(f.endYear>=A.startYear?e.push(`时段${d+1}和时段${d+2}存在重叠`):e.push(`时段${d+1}和时段${d+2}之间存在间隔`))}return e},E=e=>{if(!e||e.length===0){a.value=[{startYear:1,endYear:25,dampRate:.95}];return}const c=[];let d=e[0],f=1;for(let A=1;A<=e.length;A++)(A===e.length||e[A]!==d)&&(c.push({startYear:f,endYear:A,dampRate:d}),A<e.length&&(d=e[A],f=A+1));a.value=c.length>0?c:[{startYear:1,endYear:e.length,dampRate:e[0]||.95}]};return X(()=>g.modelValue,e=>{e&&(b.value.manufacturer=e.manufacturer||"",b.value.model=e.model||"",e.dampCurve&&e.dampCurve.length>0?E(e.dampCurve):E([]))},{immediate:!0,deep:!0}),B({validate:async()=>{try{await h.value.validateFields();const e=z();return e.length>0?(P.error(`时间段配置错误：${e.join("；")}`),!1):!0}catch(e){return console.log("表单验证失败:",e),!1}},resetFields:()=>{var e;b.value={manufacturer:"",model:""},a.value=[{startYear:1,endYear:25,dampRate:.95}],(e=h.value)==null||e.resetFields()},getFormData:()=>({type:1,baseInfo:{manufacturer:b.value.manufacturer,model:b.value.model},params:{damp_curve:{data:R.value}}})}),(e,c)=>{const d=_("a-input"),f=_("a-form-item"),A=_("a-button"),K=_("a-input-number"),H=_("a-form");return w(),U("div",ht,[o(H,{ref_key:"formRef",ref:h,model:b.value,rules:T,"label-col":{span:4},"wrapper-col":{span:18}},{default:s(()=>[o(f,{label:"厂商",name:"manufacturer"},{default:s(()=>[o(d,{value:b.value.manufacturer,"onUpdate:value":c[0]||(c[0]=x=>b.value.manufacturer=x),placeholder:"请输入厂商名称",disabled:C.disabled},null,8,["value","disabled"])]),_:1}),o(f,{label:"产品型号",name:"model"},{default:s(()=>[o(d,{value:b.value.model,"onUpdate:value":c[1]||(c[1]=x=>b.value.model=x),placeholder:"请输入产品型号（可选）",disabled:C.disabled},null,8,["value","disabled"])]),_:1}),o(f,{label:"衰减率配置",name:"dampCurve"},{default:s(()=>[p("div",bt,[p("div",xt,[p("div",wt,[kt,o(A,{type:"primary",size:"small",onClick:S,disabled:C.disabled},{default:s(()=>[q(" 新增时间段 ")]),_:1},8,["disabled"])]),p("div",Ct,[(w(!0),U(re,null,ce(a.value,(x,y)=>(w(),U("div",{key:y,class:"period-row"},[It,p("div",$t,[St,o(K,{value:x.startYear,"onUpdate:value":k=>x.startYear=k,min:1,size:"small",style:{width:"60px"},disabled:C.disabled},null,8,["value","onUpdate:value","disabled"]),Dt,Rt,Yt,o(K,{value:x.endYear,"onUpdate:value":k=>x.endYear=k,min:1,size:"small",style:{width:"60px"},disabled:C.disabled},null,8,["value","onUpdate:value","disabled"]),zt,o(K,{value:x.dampRate,"onUpdate:value":k=>x.dampRate=k,min:0,max:1,step:.01,size:"small",style:{width:"80px"},disabled:C.disabled},null,8,["value","onUpdate:value","disabled"]),o(A,{size:"small",danger:"",type:"text",onClick:k=>F(y),disabled:a.value.length<=1||C.disabled},{default:s(()=>[q(" 删除 ")]),_:2},1032,["onClick","disabled"])])]))),128))])])])]),_:1})]),_:1},8,["model"])])}}},Tt=ue(qt,[["__scopeId","data-v-0c60fb43"]]),Pt={__name:"PvDeviceModal",props:{visible:{type:Boolean,default:!1},isEditing:{type:Boolean,default:!1},editData:{type:Object,default:()=>({})},confirmLoading:{type:Boolean,default:!1}},emits:["update:visible","confirm","cancel"],setup(C,{emit:B}){const r=C,g=B,h=Y(),b=Y({manufacturer:"",model:"",dampCurve:[]}),a=z=>{b.value=z},R=async()=>{var M,D;if(!await((M=h.value)==null?void 0:M.validate()))return;const E=(D=h.value)==null?void 0:D.getFormData();r.isEditing&&r.editData.id&&(E.baseInfo.id=r.editData.id),g("confirm",E)},T=()=>{g("cancel"),g("update:visible",!1)},S=()=>{var z;if(r.isEditing&&r.editData){const E={manufacturer:r.editData.manufacturer||"",model:r.editData.model||"",dampCurve:((z=r.editData.damp_curve)==null?void 0:z.data)||[]};b.value=E}},F=()=>{var z;b.value={manufacturer:"",model:"",dampCurve:[]},(z=h.value)==null||z.resetFields()};return X(()=>r.visible,z=>{z&&(r.isEditing?S():F())}),X(()=>r.editData,()=>{r.visible&&r.isEditing&&S()},{deep:!0}),(z,E)=>{const M=_("a-modal");return w(),V(M,{visible:C.visible,title:C.isEditing?"修改光伏设备":"新增光伏设备",width:800,onOk:R,onCancel:T,"confirm-loading":C.confirmLoading,"destroy-on-close":""},{default:s(()=>[o(Tt,{ref_key:"editorRef",ref:h,modelValue:b.value,"onUpdate:modelValue":E[0]||(E[0]=D=>b.value=D),onChange:a},null,8,["modelValue"])]),_:1},8,["visible","title","confirm-loading"])}}},Et={__name:"index",props:{data:{type:Array,default:()=>[],required:!0},width:{type:Number,default:300},height:{type:Number,default:120},showAxis:{type:Boolean,default:!0},showTooltip:{type:Boolean,default:!0},title:{type:String,default:""}},setup(C,{expose:B}){const r=C,g=Y();let h=null;const b=()=>{if(!g.value||!r.data||r.data.length===0)return;h&&h.dispose(),h=$e(g.value);const R={title:{text:r.title,textStyle:{fontSize:12,color:"#666"},left:"center",top:5},grid:{left:r.showAxis?30:10,right:10,top:r.title?30:10,bottom:r.showAxis?25:10,containLabel:!1},xAxis:{type:"category",data:r.data.map((T,S)=>`第${S+1}年`),show:r.showAxis,axisLabel:{fontSize:10,color:"#666",interval:Math.floor(r.data.length/5)},axisLine:{lineStyle:{color:"#d9d9d9"}},axisTick:{show:!1}},yAxis:{type:"value",show:r.showAxis,axisLabel:{fontSize:10,color:"#666",formatter:"{value}"},axisLine:{lineStyle:{color:"#d9d9d9"}},splitLine:{lineStyle:{color:"#f0f0f0",type:"dashed"}}},tooltip:{show:r.showTooltip,trigger:"axis",backgroundColor:"rgba(0, 0, 0, 0.8)",borderColor:"transparent",textStyle:{color:"#fff",fontSize:12},formatter:function(T){const S=T[0].dataIndex,F=r.data[S];return`第${S+1}年<br/>衰减率: ${F}`}},series:[{type:"line",data:r.data,smooth:!0,symbol:"circle",symbolSize:4,lineStyle:{color:"#1890ff",width:2},itemStyle:{color:"#1890ff"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(24, 144, 255, 0.3)"},{offset:1,color:"rgba(24, 144, 255, 0.05)"}]}}}]};h.setOption(R)},a=()=>{if(!h||!r.data||r.data.length===0)return;const R={xAxis:{data:r.data.map((T,S)=>`第${S+1}年`)},series:[{data:r.data}]};h.setOption(R)};return X(()=>r.data,()=>{ae(()=>{r.data&&r.data.length>0&&(h?a():b())})},{deep:!0}),X([()=>r.width,()=>r.height],()=>{ae(()=>{h&&h.resize()})}),pe(()=>{ae(()=>{b()})}),Ie(()=>{h&&(h.dispose(),h=null)}),B({resize:()=>{h&&h.resize()}}),(R,T)=>(w(),U("div",{ref_key:"chartRef",ref:g,class:"echarts-line-chart",style:Ne({width:C.width+"px",height:C.height+"px"})},null,4))}},Mt=ue(Et,[["__scopeId","data-v-677b669a"]]),De=C=>(ve("data-v-8a4910a7"),C=C(),_e(),C),At={class:"body_wrap"},Bt={class:"p_wrap"},Ut=De(()=>p("div",{class:"title_wrap"},null,-1)),Ft={class:"content_wrap",id:"content_wrap"},Lt={class:"part_wrap"},Nt=De(()=>p("div",{class:"p_title"},[p("div",null,"设备配置"),p("div",{class:"btn_wrap"})],-1)),Kt={class:"tab_wrap"},Ot={key:0,class:"t_btn_wrap"},Vt=["onClick"],Zt=["onClick"],jt={key:1,class:"no-data"},Ht={key:0,class:"t_btn_wrap"},Gt=["onClick"],Wt=["onClick"],Qt={key:0,class:"t_btn_wrap"},Jt=["onClick"],Xt=["onClick"],ea={key:0,class:"t_btn_wrap"},ta=["onClick"],aa=["onClick"],na={__name:"index",setup(C){be.useModal(),Ke();const B=Y(!1),r=Y(!1),g=Y(!1),h=Y(),b=Y([]),a=Y({}),R=Y({}),T=Y(!1),S=Y(1);Y([]);const F=Y(!1),z=Y(me().filter(x=>x.s_type)),E=[{label:"光伏",value:1},{label:"风机",value:2},{label:"储能",value:3},{label:"电解槽",value:4},{label:"储罐",value:5}],M=Y({}),D=x=>[we,Oe,function(){return[]},me,ke,Ce][x]().filter(k=>k.s_type),I=(x,y)=>{be.confirm({title:"确认删除?",async onOk(){const{code:k,msg:L}=await Ue({devIdList:[x.id]});k===0?(P.success("删除成功"),H()):P.error(L)}})},e=(x,y)=>{F.value=!0,R.value=fe.cloneDeep(x),M.value=E.find(k=>k.value===y),y===1?g.value=!0:(r.value=!0,z.value=D(y),a.value=fe.cloneDeep(x))},c=x=>{F.value=!1,R.value={},M.value=E.find(y=>y.value===x),x===1?g.value=!0:(r.value=!0,z.value=D(x),console.log("form item:",z.value,x))},d=x=>{S.value=x,H()},f=async()=>{var ee;await h.value.validateFields();const x=S.value,y={manufacturer:a.value.manufacturer,model:a.value.model};let k,L;T.value=!0;const Z=fe.cloneDeep(a.value);if(delete Z.manufacturer,delete Z.model,delete Z.id,delete Z.category,F.value){y.id=(ee=R.value)==null?void 0:ee.id;const t=await ge([{type:x,baseInfo:y,params:Z}]);k=t.code,L=t.msg}else{const t=await ye([{type:x,baseInfo:y,params:Z}]);k=t.code,L=t.msg}T.value=!1,k===0?H():P.error(L),r.value=!1},A=async x=>{T.value=!0;try{let y,k;if(F.value){const L=await ge([x]);y=L.code,k=L.msg}else{const L=await ye([x]);y=L.code,k=L.msg}y===0?(P.success(F.value?"修改成功":"新增成功"),g.value=!1,H()):P.error(k)}catch(y){P.error("操作失败"),console.error("光伏设备操作失败:",y)}finally{T.value=!1}},K=()=>{g.value=!1},H=async()=>{B.value=!0;const{code:x,data:y,msg:k}=await Be({type:S.value});x===0?(b.value=y.result.map(L=>{const{baseInfo:Z,params:ee}=L;return{...Z,...ee}}),console.log("table:",b.value)):P.error(k),B.value=!1};return pe(()=>{H()}),(x,y)=>{const k=_("a-button"),L=_("a-table"),Z=_("a-tab-pane"),ee=_("a-tag"),t=_("a-tabs"),n=_("a-input-number"),l=_("a-input"),u=_("a-select-option"),i=_("a-select"),m=_("a-form-item"),N=_("a-form"),j=_("a-modal");return w(),U("div",At,[p("div",Bt,[Ut,p("div",Ft,[p("div",Lt,[Nt,p("div",Kt,[o(t,{activeKey:S.value,"onUpdate:activeKey":y[4]||(y[4]=v=>S.value=v),onChange:d},{default:s(()=>[(w(),V(Z,{key:1,tab:"光伏"},{default:s(()=>[o(k,{style:{margin:"-10px 0 10px 0"},size:"small",onClick:y[0]||(y[0]=v=>c(1))},{default:s(()=>[q("新增")]),_:1}),o(L,{size:"small",loading:B.value,pagination:!1,columns:te(we)(),rowKey:"id","data-source":b.value,defaultExpandAllRows:!0},{bodyCell:s(({column:v,record:$})=>[v.key==="action"?(w(),U("div",Ot,[p("a",{class:"a_item",onClick:G=>e($,1)},"修改",8,Vt),p("a",{class:"a_item",onClick:G=>I($,1)},"删除",8,Zt)])):J("",!0),v.key==="damp_curve"?(w(),U(re,{key:1},[$.damp_curve&&$.damp_curve.data&&$.damp_curve.data.length>0?(w(),V(Mt,{key:0,data:$.damp_curve.data,width:150,height:60,"show-axis":!1,"show-tooltip":!0},null,8,["data"])):(w(),U("span",jt,"未配置"))],64)):J("",!0)]),_:1},8,["loading","columns","data-source"])]),_:1})),(w(),V(Z,{key:3,tab:"储能"},{default:s(()=>[o(k,{style:{margin:"-10px 0 10px 0"},size:"small",onClick:y[1]||(y[1]=v=>c(3))},{default:s(()=>[q("新增")]),_:1}),o(L,{size:"small",loading:B.value,pagination:!1,columns:te(me)(),rowKey:"id","data-source":b.value,defaultExpandAllRows:!0},{bodyCell:s(({column:v,record:$})=>[v.key==="action"?(w(),U("div",Ht,[p("a",{class:"a_item",onClick:G=>e($,3)},"修改",8,Gt),p("a",{class:"a_item",onClick:G=>I($,3)},"删除",8,Wt)])):J("",!0)]),_:1},8,["loading","columns","data-source"])]),_:1})),(w(),V(Z,{key:4,tab:"电解槽"},{default:s(()=>[o(k,{style:{margin:"-10px 0 10px 0"},size:"small",onClick:y[2]||(y[2]=v=>c(4))},{default:s(()=>[q("新增")]),_:1}),o(L,{size:"small",loading:B.value,pagination:!1,columns:te(ke)(),rowKey:"id","data-source":b.value,defaultExpandAllRows:!0},{bodyCell:s(({column:v,record:$,text:G})=>{var le;return[v.key==="action"?(w(),U("div",Qt,[p("a",{href:"void:0",class:"a_item",onClick:oe=>e($,4)},"修改",8,Jt),p("a",{href:"void:0",class:"a_item",onClick:oe=>I($,4)},"删除",8,Xt)])):J("",!0),v.key==="ele_type"?(w(),V(ee,{key:1,color:(le=te(xe)(v.options,G))==null?void 0:le.color},{default:s(()=>{var oe;return[q(ie((oe=te(xe)(v.options,G))==null?void 0:oe.label),1)]}),_:2},1032,["color"])):J("",!0)]}),_:1},8,["loading","columns","data-source"])]),_:1})),(w(),V(Z,{key:5,tab:"储罐"},{default:s(()=>[o(k,{style:{margin:"-10px 0 10px 0"},size:"small",onClick:y[3]||(y[3]=v=>c(5))},{default:s(()=>[q("新增")]),_:1}),o(L,{size:"small",loading:B.value,pagination:!1,columns:te(Ce)(),rowKey:"id","data-source":b.value,defaultExpandAllRows:!0},{bodyCell:s(({column:v,record:$})=>[v.key==="action"?(w(),U("div",ea,[p("a",{href:"void:0",class:"a_item",onClick:G=>e($,5)},"修改",8,ta),p("a",{href:"void:0",class:"a_item",onClick:G=>I($,5)},"删除",8,aa)])):J("",!0)]),_:1},8,["loading","columns","data-source"])]),_:1})),(w(),V(Z,{key:6,tab:"电价"},{default:s(()=>[o(yt)]),_:1}))]),_:1},8,["activeKey"])])])])]),o(Pt,{visible:g.value,"onUpdate:visible":y[5]||(y[5]=v=>g.value=v),"is-editing":F.value,"edit-data":R.value,"confirm-loading":T.value,onConfirm:A,onCancel:K},null,8,["visible","is-editing","edit-data","confirm-loading"]),o(j,{open:r.value,"onUpdate:open":y[6]||(y[6]=v=>r.value=v),title:F.value?`修改${M.value.label}设备`:`新增${M.value.label}设备`,onOk:f,"confirm-loading":T.value,destroyOnClose:""},{default:s(()=>[p("div",null,[o(N,{labelAlign:"left2",ref_key:"formRef",ref:h,model:a.value,name:"basic","label-col":{span:6},"wrapper-col":{span:16},autocomplete:"off"},{default:s(()=>[(w(!0),U(re,null,ce(z.value,v=>(w(),V(m,{label:v.title,name:v.dataIndex,rules:v.rules},{default:s(()=>[v.s_type==="number"?(w(),V(n,{key:0,style:{width:"100%"},value:a.value[v.dataIndex],"onUpdate:value":$=>a.value[v.dataIndex]=$,size:"small",min:0},null,8,["value","onUpdate:value"])):v.s_type==="string"?(w(),V(l,{key:1,value:a.value[v.dataIndex],"onUpdate:value":$=>a.value[v.dataIndex]=$},null,8,["value","onUpdate:value"])):v.s_type==="select"?(w(),V(i,{key:2,value:a.value[v.dataIndex],"onUpdate:value":$=>a.value[v.dataIndex]=$,style2:"width: 120px"},{default:s(()=>[(w(!0),U(re,null,ce(v.options,$=>(w(),V(u,{value:$.value},{default:s(()=>[q(ie($.label),1)]),_:2},1032,["value"]))),256))]),_:2},1032,["value","onUpdate:value"])):J("",!0)]),_:2},1032,["label","name","rules"]))),256))]),_:1},8,["model"])])]),_:1},8,["open","title","confirm-loading"])])}}},ua=ue(na,[["__scopeId","data-v-8a4910a7"]]);export{ua as default};
